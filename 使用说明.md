# 股票指标批量计算系统使用说明

## 🎯 系统特性

1. **高性能计算**: 多进程并行计算，支持大规模数据处理
2. **增量更新**: 智能检测最新日期，只计算缺失的指标
3. **Parquet存储**: 高效的列式存储，支持快速查询和分析
4. **灵活配置**: 支持指定日期、股票范围等多种计算模式
5. **向后兼容**: 兼容现有的DuckDB存储格式

## 📦 安装和配置

确保使用正确的Python环境：
```bash
D:\miniconda3\envs\py311\python.exe
```

## 🚀 使用方法

### 1. 批量计算指标

#### 增量计算（推荐）
```bash
# 自动从最新日期开始增量计算
D:\miniconda3\envs\py311\python.exe scripts/calculate_indicators_batch.py --mode incremental

# 计算最近7天
D:\miniconda3\envs\py311\python.exe scripts/calculate_indicators_batch.py --mode incremental --days 7

# 指定日期范围
D:\miniconda3\envs\py311\python.exe scripts/calculate_indicators_batch.py --mode incremental --start-date 2025-01-01 --end-date 2025-01-15
```

#### 按日期计算
```bash
# 计算指定日期
D:\miniconda3\envs\py311\python.exe scripts/calculate_indicators_batch.py --mode date --date 2025-01-15

# 计算日期范围
D:\miniconda3\envs\py311\python.exe scripts/calculate_indicators_batch.py --mode date --start-date 2025-01-10 --end-date 2025-01-15

# 强制重新计算
D:\miniconda3\envs\py311\python.exe scripts/calculate_indicators_batch.py --mode date --date 2025-01-15 --force
```

#### 按股票计算
```bash
# 计算指定股票
D:\miniconda3\envs\py311\python.exe scripts/calculate_indicators_batch.py --mode symbols --symbols 000001,000002,600000

# 计算所有股票（限制数量）
D:\miniconda3\envs\py311\python.exe scripts/calculate_indicators_batch.py --mode symbols --limit 100

# 指定时间范围
D:\miniconda3\envs\py311\python.exe scripts/calculate_indicators_batch.py --mode symbols --symbols 000001,000002 --start-date 2024-01-01
```

#### 查看状态
```bash
# 查看当前指标数据状态
D:\miniconda3\envs\py311\python.exe scripts/calculate_indicators_batch.py --mode status
```

### 2. 增量更新

#### 日常增量更新
```bash
# 更新到最新日期（推荐日常使用）
D:\miniconda3\envs\py311\python.exe scripts/update_indicators_incremental.py

# 更新到指定日期
D:\miniconda3\envs\py311\python.exe scripts/update_indicators_incremental.py --target-date 2025-01-15

# 更新最近N天
D:\miniconda3\envs\py311\python.exe scripts/update_indicators_incremental.py --days 3
```

#### 指定股票更新
```bash
# 只更新指定股票
D:\miniconda3\envs\py311\python.exe scripts/update_indicators_incremental.py --symbols 000001,000002

# 更新指定日期的指定股票
D:\miniconda3\envs\py311\python.exe scripts/update_indicators_incremental.py --date 2025-01-15 --symbols 000001,000002
```

#### 错误重试
```bash
# 重试失败的日期
D:\miniconda3\envs\py311\python.exe scripts/update_indicators_incremental.py --retry

# 查看更新状态
D:\miniconda3\envs\py311\python.exe scripts/update_indicators_incremental.py --status
```

### 3. 数据迁移

如果你有现有的DuckDB格式数据，可以迁移到Parquet：

```bash
# 分析现有DuckDB数据
D:\miniconda3\envs\py311\python.exe scripts/migrate_duckdb_to_parquet.py --mode analyze

# 按股票迁移（推荐）
D:\miniconda3\envs\py311\python.exe scripts/migrate_duckdb_to_parquet.py --mode migrate-symbol --force

# 按日期迁移
D:\miniconda3\envs\py311\python.exe scripts/migrate_duckdb_to_parquet.py --mode migrate-date --force

# 验证迁移结果
D:\miniconda3\envs\py311\python.exe scripts/migrate_duckdb_to_parquet.py --mode verify
```

### 4. 测试和验证

```bash
# 运行基本测试
D:\miniconda3\envs\py311\python.exe simple_test.py

# 运行完整测试
D:\miniconda3\envs\py311\python.exe scripts/test_indicators.py
```

## ⚙️ 配置参数

### 性能配置
- `--workers`: 工作进程数（默认4）
- `--batch-size`: 批处理大小（默认100）

### 数据范围
- `--symbols`: 股票代码列表，逗号分隔
- `--limit`: 限制处理的股票数量
- `--date`: 指定日期 (YYYY-MM-DD)
- `--start-date`: 开始日期
- `--end-date`: 结束日期
- `--days`: 最近N天

### 其他选项
- `--force`: 强制重新计算
- `--status`: 显示状态信息

## 📊 数据存储格式

### Parquet文件结构
```
data/strategy/processed/indicators/
├── by_date/           # 按日期存储，便于日度分析
│   ├── 2024/
│   │   ├── 01/
│   │   │   ├── 20240101.parquet  # 包含当日所有股票的指标
│   │   │   └── 20240102.parquet
│   │   └── 02/
│   └── 2025/
└── by_symbol/         # 按股票存储，便于个股分析
    ├── 000001.parquet  # 包含该股票的所有历史指标
    ├── 000002.parquet
    └── 600000.parquet
```

### 指标列说明
- 基础数据：symbol, date, open, high, low, close, volume
- 趋势指标：MACD, BOLL, TRIX, SMA, EMA, DMA, AMA
- 震荡指标：RSI, KDJ, CCI, WR, ROC, PSY, BIAS
- 成交量指标：OBV, VR, MAVR, MFI, VWMA
- 动量指标：DMI, PDI, MDI, DX, ADX, ADXR
- 其他指标：CR, TEMA, PPO, WT, SUPERTREND, DPO, VHF, RVI, FI, ENE, STOCHRSI

## 🔧 在代码中使用

### 读取指标数据
```python
from app.strategy.indicators.parquet_storage import ParquetIndicatorStorage

storage = ParquetIndicatorStorage()

# 按股票读取
data = storage.load_indicators_by_symbol('000001', '2024-01-01', '2024-12-31')

# 按日期读取
data = storage.load_indicators_by_date('2025-01-15')

# 获取状态信息
latest_date = storage.get_latest_date()
symbols = storage.get_available_symbols()
```

### 使用服务层
```python
from app.strategy.services.indicator_service import HighPerformanceIndicatorService

service = HighPerformanceIndicatorService()

# 获取指标数据（自动从Parquet读取）
indicators = service.calculate_indicators('000001', '2024-01-01', '2024-12-31')

# 获取指标摘要
summary = service.get_indicator_summary('000001')
```

## 📝 注意事项

1. **首次使用**: 建议先运行增量计算来生成初始数据
2. **性能优化**: 根据机器配置调整workers参数
3. **存储空间**: Parquet文件会占用一定磁盘空间，定期清理旧数据
4. **数据一致性**: 建议在非交易时间进行大批量计算
5. **错误处理**: 如遇到错误，可使用重试功能

## 🚨 故障排除

### 常见问题
1. **内存不足**: 减少workers数量或batch-size
2. **文件权限**: 确保对data目录有写权限
3. **数据缺失**: 检查原始数据是否存在
4. **计算失败**: 查看日志文件了解详细错误

### 日志文件
- `logs/batch_indicators.log` - 批量计算日志
- `logs/incremental_update.log` - 增量更新日志
- `logs/migration.log` - 数据迁移日志

## 🎯 最佳实践

1. **日常使用**: 每日运行增量更新
2. **初始化**: 使用批量计算生成历史数据
3. **监控**: 定期检查状态和日志
4. **备份**: 重要数据建议定期备份
5. **测试**: 在生产环境使用前先测试
