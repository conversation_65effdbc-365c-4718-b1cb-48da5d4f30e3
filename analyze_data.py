#!/usr/bin/env python3
"""
分析数据文件的脚本
"""
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta

def analyze_data_files():
    """分析数据文件"""
    print("=== 数据文件分析 ===")
    
    # 检查最近的数据文件
    data_path = Path('data/strategy/raw/2025/07')
    if not data_path.exists():
        print(f"数据路径不存在: {data_path}")
        return
        
    files = sorted(data_path.glob('*.parquet'))
    
    print(f"找到 {len(files)} 个数据文件")
    print("\n最近的数据文件:")
    for f in files[-10:]:
        try:
            df = pd.read_parquet(f)
            print(f"{f.name}: {len(df)} 条记录, {df['symbol'].nunique()} 只股票")
            if not df.empty:
                print(f"  日期范围: {df['date'].min()} 到 {df['date'].max()}")
        except Exception as e:
            print(f"{f.name}: 读取失败 - {e}")
    
    print("\n=== 计算最近10天应该包含的工作日 ===")
    today = datetime.now()
    expected_files = []
    
    for i in range(10):
        date = today - timedelta(days=i)
        if date.weekday() < 5:  # 工作日
            date_str = date.strftime('%Y%m%d')
            expected_files.append(date_str)
            file_path = data_path / f"{date_str}.parquet"
            exists = file_path.exists()
            print(f"{date.strftime('%Y-%m-%d %A')}: {date_str}.parquet {'✓' if exists else '✗'}")
    
    print(f"\n预期工作日文件数: {len(expected_files)}")
    
    # 检查7月29日之后的情况
    print("\n=== 7月29日之后的工作日 ===")
    start_date = datetime(2025, 7, 29)
    
    for i in range(10):
        date = start_date + timedelta(days=i)
        if date.weekday() < 5:  # 工作日
            date_str = date.strftime('%Y%m%d')
            
            # 检查7月和8月的文件
            july_path = Path('data/strategy/raw/2025/07') / f"{date_str}.parquet"
            august_path = Path('data/strategy/raw/2025/08') / f"{date_str}.parquet"
            
            july_exists = july_path.exists()
            august_exists = august_path.exists()
            
            print(f"{date.strftime('%Y-%m-%d %A')}: {date_str}.parquet")
            print(f"  7月目录: {'✓' if july_exists else '✗'}")
            print(f"  8月目录: {'✓' if august_exists else '✗'}")

if __name__ == "__main__":
    analyze_data_files()
