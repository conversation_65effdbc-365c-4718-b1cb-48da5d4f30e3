# 快速开始指南

## 项目概述

股票策略研报展示平台是一个基于FastAPI的Web应用，主要用于展示和分析股票研报数据。支持多数据源采集、AI分析、主题研究等功能。

## 环境要求

- Python 3.11+
- 操作系统: macOS/Linux/Windows
- 内存: 至少4GB
- 磁盘空间: 至少10GB

## 快速安装

### 1. 克隆项目
```bash
git clone <repository-url>
cd stock-strategy
```

### 2. 创建虚拟环境
```bash
# 使用venv
python -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate   # Windows

# 或使用conda
conda create -n stock-strategy python=3.11
conda activate stock-strategy
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 配置环境变量
```bash
# 复制配置文件
cp config.env.example .env

# 编辑配置文件
vim .env
```

### 5. 启动应用
```bash
# 方式1: 使用启动脚本
python run.py

# 方式2: 直接启动
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 方式3: 使用FastAPI应用
cd app && python main.py
```

### 6. 访问应用
打开浏览器访问: http://localhost:8000

## 核心功能介绍

### 1. 研报管理 📊
- **路径**: `/reports`
- **功能**: 浏览、搜索、筛选研报
- **数据源**: 知识星球、开盘啦等

### 2. 股票分析 📈
- **路径**: `/stocks/{code}`
- **功能**: 股票详情、实时行情、相关研报
- **数据源**: akshare API

### 3. AI问股 🤖
- **路径**: `/ai_stock`
- **功能**: 上传文件进行AI分析
- **支持模型**: OpenAI、Claude、Gemini

### 4. 主题分析 🎯
- **路径**: `/themes`
- **功能**: 主题概念分析、相关股票

### 5. 收藏功能 ⭐
- **路径**: `/favorites`
- **功能**: 收藏股票和研报

## 目录结构说明

```
stock-strategy/
├── app/                    # 主应用目录
│   ├── main.py            # FastAPI应用入口
│   ├── config.py          # 配置管理
│   ├── routers/           # 路由模块
│   ├── database/          # 数据库层
│   ├── data/              # 数据处理模块
│   ├── utils/             # 工具函数
│   ├── static/            # 静态资源
│   └── templates/         # HTML模板
├── dify/                  # 数据处理脚本
├── scripts/               # 维护脚本
├── test/                  # 测试文件
├── docs/                  # 文档
├── data/                  # 数据存储
├── logs/                  # 日志文件
└── cache/                 # 缓存文件
```

## 配置说明

### 基础配置 (.env)
```bash
# API服务配置
API_HOST=localhost
API_PORT=8001

# 数据目录配置
REPORT_DATA_DIR=/path/to/your/data/reports
THEME_DATA_DIR=/path/to/your/data/themes
```

### AI模型配置
```bash
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Claude配置
CLAUDE_API_KEY=your_claude_api_key
CLAUDE_MODEL=claude-3-sonnet-20240229

# Gemini配置
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL=gemini-2.0-flash
```

### 代理配置 (可选)
```bash
# 如果需要代理访问
HTTP_PROXY=http://proxy.example.com:8080
HTTPS_PROXY=https://proxy.example.com:8080
```

## 数据库说明

### 数据库类型
- **主数据库**: DuckDB (高性能分析型数据库)
- **位置**: `data/database.duckdb`
- **ORM**: SQLAlchemy 2.0

### 主要数据表
- `favorite_stocks`: 收藏股票
- `favorite_reports`: 收藏研报
- `ai_stock_results`: AI分析结果
- `stock_dividends`: 股息数据
- `industries`: 行业分类

### 数据库管理
```bash
# 查看数据库状态
python -c "from app.utils.db_manager import get_db_manager; db = get_db_manager(); db.connect(); print('数据库连接成功')"

# 初始化数据库表
python scripts/simple_validation.py
```

## 数据采集

### 知识星球数据
```bash
# 配置知识星球爬虫
cd app/data/zsxq/
python zsxq_download_api.py
```

### 集思录数据
```bash
# 获取股息率数据
cd app/data/jisilu/
python get_jisilu_ind_data.py
```

### 开盘啦数据
```bash
# 自动收集研报
cd dify/
python auto_collect_reports.py
```

## 常用API接口

### 研报相关
```bash
# 获取研报列表
GET /reports/api/list?page=1&page_size=10

# 获取研报详情
GET /reports/api/{report_id}

# 搜索研报
GET /reports/api/list?search=关键词
```

### 股票相关
```bash
# 获取股票信息
GET /stocks/api/{stock_code}

# 获取股票相关研报
GET /stocks/api/{stock_code}/reports
```

### AI分析
```bash
# 上传文件分析
POST /ai_stock/api/upload

# 开始分析
POST /ai_stock/api/analyze
```

## 开发调试

### 启用调试模式
```bash
# 设置环境变量
export DEBUG=true

# 启动应用
uvicorn app.main:app --reload --log-level debug
```

### 查看日志
```bash
# 应用日志
tail -f logs/app.utils.db_manager_*.log

# API日志
tail -f custom_logs/api_*.log

# 数据处理日志
tail -f logs/zsxq_data_*.log
```

### 数据库调试
```bash
# 连接数据库
python -c "
import duckdb
conn = duckdb.connect('data/database.duckdb')
print(conn.execute('SHOW TABLES').fetchall())
"
```

## 常见问题

### 1. 启动失败
**问题**: 应用启动时报错
**解决**:
```bash
# 检查Python版本
python --version

# 检查依赖
pip list | grep fastapi

# 检查端口占用
lsof -i :8000
```

### 2. 数据库连接失败
**问题**: 数据库连接错误
**解决**:
```bash
# 检查数据库文件
ls -la data/database.duckdb

# 检查权限
chmod 644 data/database.duckdb

# 重新初始化
rm data/database.duckdb
python scripts/simple_validation.py
```

### 3. AI功能不可用
**问题**: AI分析功能报错
**解决**:
```bash
# 检查API密钥
echo $OPENAI_API_KEY

# 检查网络连接
curl -I https://api.openai.com

# 检查代理设置
echo $HTTP_PROXY
```

### 4. 数据采集失败
**问题**: 数据采集脚本报错
**解决**:
```bash
# 检查数据目录
ls -la data/

# 检查网络连接
ping www.baidu.com

# 查看详细错误
python app/data/zsxq/zsxq_download_api.py --verbose
```

## 性能优化

### 1. 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_reports_date ON reports(create_time);
CREATE INDEX idx_reports_code ON reports(code_list);
```

### 2. 缓存配置
```python
# 启用文件缓存
CACHE_ENABLED=true
CACHE_TTL=3600
```

### 3. 并发配置
```bash
# 增加工作进程
uvicorn app.main:app --workers 4
```

## 部署建议

### 开发环境
```bash
# 使用开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 生产环境
```bash
# 使用Gunicorn
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### Docker部署
```bash
# 构建镜像
docker build -t stock-strategy .

# 运行容器
docker run -p 8000:8000 -v $(pwd)/data:/app/data stock-strategy
```

## 下一步

1. **熟悉功能**: 浏览各个功能模块
2. **配置数据源**: 设置数据采集
3. **配置AI**: 设置AI模型API密钥
4. **自定义开发**: 根据需求进行定制
5. **性能调优**: 根据使用情况优化性能

## 获取帮助

- **文档**: 查看 `docs/` 目录下的详细文档
- **日志**: 查看 `logs/` 目录下的日志文件
- **测试**: 运行 `test/` 目录下的测试用例
- **示例**: 参考 `scripts/` 目录下的示例脚本

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

欢迎贡献代码和提出改进建议！
