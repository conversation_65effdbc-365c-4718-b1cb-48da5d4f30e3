# 研报数据API导入功能使用指南

## 概述

为了解决DuckDB数据库被服务进程锁定时无法直接运行导入脚本的问题，我们提供了基于API接口的数据导入功能。这个功能允许您通过HTTP API来导入研报数据，避免了数据库锁定问题。

## 功能特性

- ✅ 通过API接口导入数据，避免数据库锁定
- ✅ 支持后台异步导入，不阻塞API响应
- ✅ 实时进度监控和状态查询
- ✅ 支持增量导入，避免重复数据
- ✅ 支持指定日期导入
- ✅ 数据预览功能
- ✅ 任务管理和状态查询

## API接口

### 1. 启动导入任务

**POST** `/data/import/reports`

启动研报数据导入任务。

**请求参数：**
```json
{
  "data_dir": "/path/to/data/directory",
  "force_update": false,
  "dates": ["2024-01-01", "2024-01-02"]
}
```

**响应：**
```json
{
  "status": "success",
  "message": "导入任务已启动",
  "data": {
    "task_id": "import_20241201_143022_123456"
  }
}
```

### 2. 查询任务状态

**GET** `/data/import/reports/status/{task_id}`

查询指定导入任务的状态和进度。

**响应：**
```json
{
  "status": "success",
  "data": {
    "task_id": "import_20241201_143022_123456",
    "status": "running",
    "progress": {
      "current_file": "report_123_2024-01-01.json",
      "imported": 10,
      "updated": 5,
      "skipped": 2,
      "errors": 0
    },
    "message": "正在导入数据...",
    "created_at": "2024-12-01T14:30:22",
    "completed_at": null
  }
}
```

### 3. 获取任务列表

**GET** `/data/import/reports/tasks?limit=10`

获取所有导入任务的列表。

**响应：**
```json
{
  "status": "success",
  "data": {
    "tasks": [
      {
        "task_id": "import_20241201_143022_123456",
        "status": "completed",
        "progress": {
          "imported": 100,
          "updated": 20,
          "skipped": 10,
          "errors": 0
        },
        "message": "导入完成！新导入: 100, 更新: 20, 跳过: 10, 错误: 0",
        "created_at": "2024-12-01T14:30:22",
        "completed_at": "2024-12-01T14:35:15"
      }
    ],
    "total": 1,
    "limit": 10
  }
}
```

### 4. 预览数据

**GET** `/data/import/reports/preview?data_dir=/path/to/data&limit=10`

预览要导入的数据文件。

**响应：**
```json
{
  "status": "success",
  "data": {
    "data_dir": "/path/to/data",
    "total_files": 50,
    "preview_files": [
      {
        "file_path": "/path/to/data/report_123.json",
        "file_name": "report_123.json",
        "file_size": 2048,
        "report_id": "123",
        "title": "某股票研报标题...",
        "create_time": "2024-01-01 10:00:00",
        "code_list": ["000001", "000002"]
      }
    ],
    "limit": 10
  }
}
```

### 5. 删除任务记录

**DELETE** `/data/import/reports/tasks/{task_id}`

删除已完成或失败的导入任务记录。

## 客户端脚本使用

我们提供了一个Python客户端脚本 `app/scripts/import_reports_via_api.py`，方便您使用API导入功能。

### 基本用法

```bash
# 导入所有数据
python app/scripts/import_reports_via_api.py --data-dir /path/to/data

# 强制更新已存在的数据
python app/scripts/import_reports_via_api.py --data-dir /path/to/data --force-update

# 导入指定日期的数据
python app/scripts/import_reports_via_api.py --data-dir /path/to/data --dates 2024-01-01 2024-01-02

# 预览数据
python app/scripts/import_reports_via_api.py --data-dir /path/to/data --preview

# 查看任务列表
python app/scripts/import_reports_via_api.py --list-tasks

# 查看特定任务状态
python app/scripts/import_reports_via_api.py --task-id import_20241201_143022_123456
```

### 脚本参数

- `--api-url`: API服务URL（默认：http://localhost:8000）
- `--data-dir`: 研报数据目录路径
- `--force-update`: 强制更新已存在的数据
- `--dates`: 指定要导入的日期列表（YYYY-MM-DD格式）
- `--preview`: 预览数据而不导入
- `--list-tasks`: 列出所有导入任务
- `--task-id`: 获取指定任务的状态
- `--verbose`: 显示详细日志

## 使用流程

### 1. 启动API服务

确保您的API服务正在运行：

```bash
python app/main.py
```

### 2. 预览数据（可选）

在导入之前，可以先预览要导入的数据：

```bash
python app/scripts/import_reports_via_api.py --data-dir /path/to/data --preview
```

### 3. 启动导入任务

```bash
python app/scripts/import_reports_via_api.py --data-dir /path/to/data
```

脚本会自动：
1. 调用API启动导入任务
2. 获取任务ID
3. 实时监控任务进度
4. 显示导入结果

### 4. 监控任务状态

如果导入任务需要较长时间，您可以：

```bash
# 查看所有任务
python app/scripts/import_reports_via_api.py --list-tasks

# 查看特定任务状态
python app/scripts/import_reports_via_api.py --task-id <task_id>
```

## 注意事项

1. **服务必须运行**：使用API导入功能前，确保API服务正在运行
2. **数据库连接**：API服务会自动管理数据库连接，避免锁定问题
3. **后台执行**：导入任务在后台异步执行，不会阻塞API响应
4. **任务状态**：任务状态会保存在内存中，服务重启后会丢失
5. **错误处理**：如果导入过程中出现错误，任务会被标记为失败状态

## 故障排除

### 常见问题

1. **API服务未启动**
   - 错误：`Connection refused`
   - 解决：启动API服务 `python app/main.py`

2. **数据目录不存在**
   - 错误：`数据目录不存在`
   - 解决：检查数据目录路径是否正确

3. **数据库锁定**
   - 错误：`database is locked`
   - 解决：使用API导入功能，避免直接操作数据库

4. **任务状态丢失**
   - 现象：服务重启后任务状态丢失
   - 解决：重新启动导入任务

### 日志查看

启用详细日志查看：

```bash
python app/scripts/import_reports_via_api.py --verbose --data-dir /path/to/data
```

## 性能优化

1. **批量导入**：对于大量数据，建议分批导入
2. **指定日期**：使用 `--dates` 参数只导入特定日期的数据
3. **监控资源**：导入过程中监控服务器CPU和内存使用情况
4. **网络稳定**：确保客户端与API服务之间的网络连接稳定 