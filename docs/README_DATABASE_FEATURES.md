# 研报数据库管理系统

## 概述

本系统实现了基于DuckDB的研报数据管理功能，支持研报的存储、检索、评分、删除标记等操作。系统设计为可以与原有的文件系统模式共存，提供更好的性能和功能扩展性。

## 主要功能

### 1. 数据库存储
- 使用DuckDB作为后端数据库
- 支持研报的完整数据结构存储
- 包含扩展字段：评分、删除标记、统计信息等
- 支持股票代码关联表

### 2. 研报管理
- **评分系统**：0-10分评分，方便筛选高质量研报
- **软删除**：标记删除而非物理删除，可恢复
- **删除原因**：记录删除原因，便于管理
- **操作日志**：记录所有操作历史

### 3. 搜索和筛选
- **全文搜索**：支持标题和内容的关键词搜索
- **多维度筛选**：按日期、标签、股票代码、评分筛选
- **分页支持**：高效的分页查询
- **统计信息**：提供各种维度的统计数据

## 文件结构

```
app/
├── utils/
│   ├── db_manager_report.py       # 研报数据库管理器
│   ├── zsxq_data_loader_db.py     # 基于数据库的数据加载器
│   └── data_loader.py             # 原有文件系统加载器（保留）
├── scripts/
│   └── import_reports_to_db.py    # 数据导入脚本
├── routers/
│   ├── reports.py                 # 研报展示路由（已更新）
│   └── reports_management.py      # 研报管理API路由
└── templates/
    └── report_detail.html         # 研报详情页（已更新）
```

## 使用指南

### 数据导入

#### 1. 导入所有历史数据
```bash
python app/scripts/import_reports_to_db.py
```

#### 2. 导入指定日期的数据
```bash
python app/scripts/import_reports_to_db.py --dates 2024-01-15 2024-01-16
```

#### 3. 强制更新已存在的数据
```bash
python app/scripts/import_reports_to_db.py --force-update
```

#### 4. 显示详细日志
```bash
python app/scripts/import_reports_to_db.py --verbose
```

### API接口

#### 研报管理 API

**评分研报**
```http
POST /reports/manage/api/{report_id}/rating
Content-Type: application/json

{
    "rating": 8
}
```

**删除研报**
```http
DELETE /reports/manage/api/{report_id}
Content-Type: application/json

{
    "reason": "内容质量低"
}
```

**恢复研报**
```http
POST /reports/manage/api/{report_id}/restore
```

**获取研报状态**
```http
GET /reports/manage/api/{report_id}/status
```

**全文搜索**
```http
GET /reports/manage/api/search?keyword=市场&limit=50
```

**获取统计信息**
```http
GET /reports/manage/api/statistics
```

**获取已删除研报列表**
```http
GET /reports/manage/api/deleted?page=1&page_size=10
```

#### 原有研报API（已增强）

**获取研报列表**
```http
GET /reports/api/list?page=1&page_size=10&search=关键词&rating_min=5
```

### 前端功能

#### 研报详情页新功能
1. **评分功能**：下拉菜单选择0-10分
2. **删除功能**：可输入删除原因
3. **恢复功能**：针对已删除的研报
4. **状态显示**：显示当前评分和删除状态

#### 研报列表页增强
1. **搜索框**：支持关键词搜索
2. **评分筛选**：按最低评分筛选
3. **扩展信息**：显示评分、浏览量等

## 数据库表结构

### reports 主表
```sql
CREATE TABLE reports (
    id VARCHAR PRIMARY KEY,           -- 研报ID
    title TEXT NOT NULL,              -- 标题
    content TEXT,                     -- 内容
    files_data TEXT,                  -- 文件数据（JSON）
    pics_data TEXT,                   -- 图片数据（JSON）
    create_time TIMESTAMP,            -- 创建时间
    label VARCHAR,                    -- 标签
    art_url VARCHAR,                  -- 原文链接
    code_list VARCHAR,                -- 股票代码列表
    
    -- 扩展字段
    rating INTEGER DEFAULT 0,         -- 评分 (0-10)
    is_deleted BOOLEAN DEFAULT FALSE, -- 是否删除
    delete_reason TEXT,               -- 删除原因
    import_time TIMESTAMP DEFAULT now(), -- 导入时间
    update_time TIMESTAMP DEFAULT now(), -- 更新时间
    
    -- 统计字段
    view_count INTEGER DEFAULT 0,     -- 浏览次数
    like_count INTEGER DEFAULT 0,     -- 点赞次数
    comment_count INTEGER DEFAULT 0,  -- 评论次数
    
    -- 其他扩展字段
    tags TEXT,                        -- 标签
    priority INTEGER DEFAULT 0,       -- 优先级
    source VARCHAR DEFAULT 'zsxq',    -- 数据源
    quality_score FLOAT DEFAULT 0.0   -- 质量评分
);
```

### report_stocks 关联表
```sql
CREATE TABLE report_stocks (
    report_id VARCHAR NOT NULL,       -- 研报ID
    stock_code VARCHAR NOT NULL,      -- 股票代码
    created_at TIMESTAMP DEFAULT now(),
    PRIMARY KEY (report_id, stock_code)
);
```

### report_operations 操作日志表
```sql
CREATE TABLE report_operations (
    id VARCHAR PRIMARY KEY,           -- 操作ID
    report_id VARCHAR NOT NULL,       -- 研报ID
    operation_type VARCHAR NOT NULL,  -- 操作类型
    old_value TEXT,                   -- 旧值
    new_value TEXT,                   -- 新值
    operator VARCHAR,                 -- 操作者
    operation_time TIMESTAMP DEFAULT now(), -- 操作时间
    notes TEXT                        -- 备注
);
```

## 测试验证

运行测试脚本验证功能：
```bash
python test_import_and_db.py
```

测试内容包括：
- 数据库连接
- 数据加载器功能
- 研报管理功能
- 搜索功能

## 性能优化

### 索引策略
- `create_time` 索引：支持按时间排序
- `label` 索引：支持按标签筛选
- `rating` 索引：支持按评分筛选
- `is_deleted` 索引：支持筛选删除状态

### 查询优化
- 使用分页查询避免大结果集
- 全文搜索使用LIKE操作，可考虑升级到FTS
- 统计查询使用聚合函数

## 迁移策略

### 渐进式迁移
1. **并行运行**：新老系统并行，通过配置切换
2. **数据同步**：定期将文件数据导入数据库
3. **功能验证**：逐步验证各项功能
4. **完全切换**：确认稳定后完全切换到数据库模式

### 回退方案
1. 保留原有文件系统加载器
2. 可通过配置快速切换回文件模式
3. 数据库问题不影响原有功能

## 扩展功能

### 未来可扩展的功能
1. **全文搜索引擎**：集成ElasticSearch或使用DuckDB FTS
2. **标签管理**：支持标签的增删改查
3. **用户系统**：支持多用户评分和权限管理
4. **分析报告**：基于评分和浏览数据的分析
5. **推荐系统**：基于用户行为的研报推荐
6. **批量操作**：支持批量评分、删除等操作

## 注意事项

1. **数据备份**：定期备份DuckDB数据库文件
2. **并发安全**：多用户环境下注意数据库锁定
3. **性能监控**：监控查询性能，必要时优化索引
4. **日志管理**：定期清理操作日志表
5. **磁盘空间**：监控数据库文件大小增长

## 故障排除

### 常见问题
1. **导入失败**：检查数据文件格式和权限
2. **搜索慢**：考虑添加索引或优化查询
3. **评分不显示**：检查前端JavaScript是否正确加载
4. **删除失败**：确认数据库连接状态和权限

### 日志查看
```bash
# 查看应用日志
tail -f app.log

# 查看导入日志
python app/scripts/import_reports_to_db.py --verbose
```

## 联系和支持

如有问题或建议，请联系开发团队或提交Issue。 