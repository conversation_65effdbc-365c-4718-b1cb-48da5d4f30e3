# 技术实施指南

## 项目重构技术细节

### 1. 目录结构重组脚本

#### 自动化重组脚本
```bash
#!/bin/bash
# reorganize_project.sh

echo "开始项目目录重组..."

# 创建新的目录结构
mkdir -p app/core/{config,database,cache,security}
mkdir -p app/api/{v1,middleware}
mkdir -p app/services
mkdir -p app/schemas
mkdir -p data_processors/{etf,jisilu,kpl,zsxq,common}
mkdir -p web/{static,templates,assets}
mkdir -p tests/{unit,integration,api,fixtures}
mkdir -p config/environments
mkdir -p logs/{app,data_processing,api,error}

# 移动现有文件
echo "移动配置文件..."
mv app/config.py app/core/config/settings.py
mv config.env.example config/environments/

echo "移动数据处理文件..."
mv dify/* data_processors/common/
mv app/data/etf/* data_processors/etf/
mv app/data/jisilu/* data_processors/jisilu/
mv app/data/kpl/* data_processors/kpl/
mv app/data/zsxq/* data_processors/zsxq/

echo "移动Web资源..."
mv app/static/* web/static/
mv app/templates/* web/templates/

echo "整理日志目录..."
mv logs/* logs/app/
mv custom_logs/* logs/api/

echo "目录重组完成！"
```

### 2. 数据库迁移脚本

#### SQLAlchemy模型迁移
```python
# scripts/migrate_database_models.py
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from app.database.connection import get_db_manager
from app.database.models import Base
from sqlalchemy import text

def migrate_database():
    """数据库模型迁移"""
    print("开始数据库迁移...")
    
    db_manager = get_db_manager()
    
    # 备份现有数据库
    backup_database(db_manager)
    
    # 创建新表结构
    create_new_tables(db_manager)
    
    # 迁移数据
    migrate_data(db_manager)
    
    # 验证迁移结果
    validate_migration(db_manager)
    
    print("数据库迁移完成！")

def backup_database(db_manager):
    """备份数据库"""
    import shutil
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"data/database_backup_{timestamp}.duckdb"
    
    shutil.copy2(db_manager.db_path, backup_path)
    print(f"数据库已备份到: {backup_path}")

def create_new_tables(db_manager):
    """创建新表结构"""
    with db_manager.get_session() as session:
        # 创建所有表
        Base.metadata.create_all(db_manager.engine)
        print("新表结构创建完成")

def migrate_data(db_manager):
    """迁移现有数据"""
    with db_manager.get_session() as session:
        # 这里添加具体的数据迁移逻辑
        # 例如：从旧表迁移到新表
        pass

def validate_migration(db_manager):
    """验证迁移结果"""
    with db_manager.get_session() as session:
        # 验证数据完整性
        result = session.execute(text("SELECT COUNT(*) FROM favorite_stocks"))
        count = result.scalar()
        print(f"迁移后收藏股票数量: {count}")

if __name__ == "__main__":
    migrate_database()
```

### 3. 服务层重构模板

#### 基础服务类模板
```python
# app/services/base_service.py
from abc import ABC, abstractmethod
from typing import Generic, TypeVar, List, Optional, Dict, Any
from sqlalchemy.orm import Session
from app.core.database.connection import get_db_session

T = TypeVar('T')

class BaseService(Generic[T], ABC):
    """基础服务类模板"""
    
    def __init__(self, repository_class):
        self.repository_class = repository_class
    
    async def get_by_id(self, id: str) -> Optional[T]:
        """根据ID获取单个对象"""
        async with get_db_session() as session:
            repo = self.repository_class(session)
            return await repo.get_by_id(id)
    
    async def get_list(self, 
                      page: int = 1, 
                      page_size: int = 10, 
                      **filters) -> Dict[str, Any]:
        """获取对象列表"""
        async with get_db_session() as session:
            repo = self.repository_class(session)
            
            # 计算偏移量
            offset = (page - 1) * page_size
            
            # 获取数据和总数
            items = await repo.get_list(
                limit=page_size, 
                offset=offset, 
                **filters
            )
            total = await repo.count(**filters)
            
            return {
                "items": items,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
    
    async def create(self, data: Dict[str, Any]) -> T:
        """创建新对象"""
        async with get_db_session() as session:
            repo = self.repository_class(session)
            return await repo.create(data)
    
    async def update(self, id: str, data: Dict[str, Any]) -> Optional[T]:
        """更新对象"""
        async with get_db_session() as session:
            repo = self.repository_class(session)
            return await repo.update(id, data)
    
    async def delete(self, id: str) -> bool:
        """删除对象"""
        async with get_db_session() as session:
            repo = self.repository_class(session)
            return await repo.delete(id)
```

#### 具体服务实现示例
```python
# app/services/report_service.py
from typing import List, Optional, Dict, Any
from app.services.base_service import BaseService
from app.repositories.report_repository import ReportRepository
from app.models.report import Report
from app.core.cache.manager import CacheManager

class ReportService(BaseService[Report]):
    """研报服务"""
    
    def __init__(self):
        super().__init__(ReportRepository)
        self.cache = CacheManager()
    
    async def get_by_id(self, report_id: str) -> Optional[Report]:
        """获取研报详情（带缓存）"""
        cache_key = f"report:{report_id}"
        
        # 尝试从缓存获取
        cached_report = await self.cache.get(cache_key)
        if cached_report:
            return cached_report
        
        # 从数据库获取
        report = await super().get_by_id(report_id)
        
        # 缓存结果
        if report:
            await self.cache.set(cache_key, report, ttl=3600)
        
        return report
    
    async def get_reports_by_stock(self, stock_code: str) -> List[Report]:
        """获取股票相关研报"""
        cache_key = f"stock_reports:{stock_code}"
        
        cached_reports = await self.cache.get(cache_key)
        if cached_reports:
            return cached_reports
        
        async with get_db_session() as session:
            repo = ReportRepository(session)
            reports = await repo.get_by_stock_code(stock_code)
            
            # 缓存结果
            await self.cache.set(cache_key, reports, ttl=1800)
            
            return reports
    
    async def search_reports(self, 
                           query: str, 
                           filters: Dict[str, Any]) -> Dict[str, Any]:
        """搜索研报"""
        async with get_db_session() as session:
            repo = ReportRepository(session)
            
            # 执行搜索
            results = await repo.search(query, filters)
            
            return {
                "query": query,
                "filters": filters,
                "results": results,
                "total": len(results)
            }
    
    async def get_trending_reports(self, limit: int = 10) -> List[Report]:
        """获取热门研报"""
        cache_key = f"trending_reports:{limit}"
        
        cached_reports = await self.cache.get(cache_key)
        if cached_reports:
            return cached_reports
        
        async with get_db_session() as session:
            repo = ReportRepository(session)
            reports = await repo.get_trending(limit)
            
            # 缓存结果（较短时间）
            await self.cache.set(cache_key, reports, ttl=600)
            
            return reports
```

### 4. API路由重构模板

#### 版本化API路由
```python
# app/api/v1/endpoints/reports.py
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional, List
from app.services.report_service import ReportService
from app.schemas.report import ReportResponse, ReportListResponse
from app.api.dependencies import get_report_service

router = APIRouter()

@router.get("/", response_model=ReportListResponse)
async def get_reports(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    date: Optional[str] = None,
    label: Optional[str] = None,
    stock_code: Optional[str] = None,
    search: Optional[str] = None,
    service: ReportService = Depends(get_report_service)
):
    """获取研报列表"""
    try:
        filters = {}
        if date:
            filters['date'] = date
        if label:
            filters['label'] = label
        if stock_code:
            filters['stock_code'] = stock_code
        if search:
            filters['search'] = search
        
        result = await service.get_list(
            page=page,
            page_size=page_size,
            **filters
        )
        
        return ReportListResponse(**result)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{report_id}", response_model=ReportResponse)
async def get_report(
    report_id: str,
    service: ReportService = Depends(get_report_service)
):
    """获取研报详情"""
    report = await service.get_by_id(report_id)
    
    if not report:
        raise HTTPException(status_code=404, detail="研报不存在")
    
    return ReportResponse.from_orm(report)

@router.get("/stock/{stock_code}", response_model=List[ReportResponse])
async def get_reports_by_stock(
    stock_code: str,
    service: ReportService = Depends(get_report_service)
):
    """获取股票相关研报"""
    reports = await service.get_reports_by_stock(stock_code)
    return [ReportResponse.from_orm(report) for report in reports]
```

### 5. 缓存实现

#### Redis缓存实现
```python
# app/core/cache/redis_cache.py
import json
import redis.asyncio as redis
from typing import Any, Optional
from app.core.config.settings import get_settings

class RedisCache:
    """Redis缓存实现"""
    
    def __init__(self):
        settings = get_settings()
        self.redis = redis.from_url(
            settings.redis_url,
            encoding="utf-8",
            decode_responses=True
        )
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            value = await self.redis.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            print(f"Redis get error: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """设置缓存值"""
        try:
            serialized_value = json.dumps(value, default=str)
            await self.redis.setex(key, ttl, serialized_value)
            return True
        except Exception as e:
            print(f"Redis set error: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            await self.redis.delete(key)
            return True
        except Exception as e:
            print(f"Redis delete error: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            return await self.redis.exists(key) > 0
        except Exception as e:
            print(f"Redis exists error: {e}")
            return False
```

### 6. 测试框架

#### 测试配置
```python
# tests/conftest.py
import pytest
import asyncio
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.database.models import Base
from app.core.database.connection import DatabaseManager

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def test_db():
    """测试数据库"""
    # 使用内存数据库进行测试
    engine = create_engine("duckdb:///:memory:")
    Base.metadata.create_all(engine)
    
    SessionLocal = sessionmaker(bind=engine)
    
    yield SessionLocal
    
    engine.dispose()

@pytest.fixture
async def db_session(test_db):
    """数据库会话"""
    session = test_db()
    try:
        yield session
    finally:
        session.close()
```

#### 服务测试示例
```python
# tests/unit/test_report_service.py
import pytest
from unittest.mock import Mock, AsyncMock
from app.services.report_service import ReportService
from app.models.report import Report

@pytest.fixture
def mock_repository():
    """模拟仓库"""
    repo = Mock()
    repo.get_by_id = AsyncMock()
    repo.get_list = AsyncMock()
    repo.create = AsyncMock()
    repo.update = AsyncMock()
    repo.delete = AsyncMock()
    return repo

@pytest.fixture
def report_service(mock_repository):
    """研报服务"""
    service = ReportService()
    service.repository_class = lambda session: mock_repository
    return service

@pytest.mark.asyncio
async def test_get_report_by_id(report_service, mock_repository):
    """测试根据ID获取研报"""
    # 准备测试数据
    report_id = "123"
    expected_report = Report(id=report_id, title="测试研报")
    mock_repository.get_by_id.return_value = expected_report
    
    # 执行测试
    result = await report_service.get_by_id(report_id)
    
    # 验证结果
    assert result == expected_report
    mock_repository.get_by_id.assert_called_once_with(report_id)

@pytest.mark.asyncio
async def test_get_reports_list(report_service, mock_repository):
    """测试获取研报列表"""
    # 准备测试数据
    reports = [
        Report(id="1", title="研报1"),
        Report(id="2", title="研报2")
    ]
    mock_repository.get_list.return_value = reports
    mock_repository.count.return_value = 2
    
    # 执行测试
    result = await report_service.get_list(page=1, page_size=10)
    
    # 验证结果
    assert result["items"] == reports
    assert result["total"] == 2
    assert result["page"] == 1
    assert result["page_size"] == 10
```

### 7. 部署配置

#### Docker配置
```dockerfile
# Dockerfile
FROM python:3.11-slim as builder

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 生产阶段
FROM python:3.11-slim as runtime

WORKDIR /app

# 复制依赖
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app
RUN chown -R app:app /app
USER app

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动应用
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=duckdb:///data/database.duckdb
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
    restart: unless-stopped

volumes:
  redis_data:
```

这个技术实施指南提供了具体的代码模板和配置文件，可以作为项目重构的实际参考。每个部分都包含了详细的实现代码，可以直接使用或根据具体需求进行调整。
