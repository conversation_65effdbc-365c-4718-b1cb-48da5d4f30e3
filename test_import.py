#!/usr/bin/env python3
"""
测试导入是否正常
"""
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有导入"""
    print("🧪 测试导入...")
    
    try:
        print("1. 测试 MiniQMTCollector 导入...")
        from app.strategy.data.miniqmt_collector import MiniQMTCollector
        print("   ✅ MiniQMTCollector 导入成功")
        
        print("2. 测试 DataManager 导入...")
        from app.strategy.data.manager import DataManager
        print("   ✅ DataManager 导入成功")
        
        print("3. 测试 settings 导入...")
        from app.strategy.config import settings
        print("   ✅ settings 导入成功")
        
        print("4. 测试 data 模块导入...")
        from app.strategy.data import DataCollector, DataManager
        print("   ✅ data 模块导入成功")
        
        print("5. 测试创建实例...")
        collector = DataCollector()
        manager = DataManager()
        print("   ✅ 实例创建成功")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_imports()
