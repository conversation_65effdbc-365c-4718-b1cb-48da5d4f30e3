#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦研报自动采集运行示例
"""

from auto_collect_reports import AutoReportCollector

def main():
    """运行示例"""
    print("=== 开盘啦研报自动采集示例 ===\n")
    
    # 初始化采集器
    collector = AutoReportCollector()
    
    # 1. 查看当前状态
    print("1. 查看当前采集状态:")
    status = collector.get_collection_status()
    print(f"   报告目录: {status['report_dir']}")
    print(f"   目录存在: {'是' if status['exists'] else '否'}")
    print(f"   最大报告ID: {status['max_id']}")
    print(f"   文件数量: {status['file_count']}")
    if status['exists']:
        print(f"   下次起始ID: {status['next_start_id']}")
    print()
    
    # 2. 执行自动采集（基于最大ID，采集10个作为示例）
    print("2. 开始自动采集 (采集10个作为示例):")
    try:
        results = collector.collect_reports(collect_count=10)
        
        print(f"   采集完成!")
        print(f"   采集范围: {results['meta']['start_id']} - {results['meta']['end_id']}")
        print(f"   成功获取: {results['stats']['success_count']}")
        print(f"   获取失败: {results['stats']['error_count']}")
        print(f"   有效数据: {results['valid_count']}")
        
        # 显示有效数据示例
        if results['valid_data']:
            print(f"\n   数据示例:")
            for i, report in enumerate(results['valid_data'][:2]):
                print(f"   [{i+1}] ID: {report['Id']} - {report['Title'][:50]}...")
                print(f"       时间: {report['CreateTime']}")
                print(f"       股票: {', '.join(report['code_list']) if report['code_list'] else '无'}")
        
    except Exception as e:
        print(f"   采集失败: {str(e)}")
    
    print("\n=== 示例完成 ===")
    print("你可以使用以下命令进行实际采集:")
    print("python auto_collect_reports.py --count 100  # 采集100个")
    print("python auto_collect_reports.py --status     # 查看状态")
    print("python auto_collect_reports.py --start 213030 --count 50  # 指定起始ID")

if __name__ == "__main__":
    main() 