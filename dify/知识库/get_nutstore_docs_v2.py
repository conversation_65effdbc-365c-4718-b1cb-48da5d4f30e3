#!/usr/bin/env python
# -*- coding: utf-8 -*-

import base64
import os
import re
import json
import requests
import xml.etree.ElementTree as ET
from urllib.parse import urlparse, unquote
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any

class NutstoreAPI:
    """坚果云WebDAV API客户端，支持获取文档内容"""

    def __init__(self, username: str, password: str, dav_url: str = "dav.jianguoyun.com/dav/SimpRead/md"):
        """初始化坚果云API客户端

        Args:
            username (str): 坚果云账户名/邮箱
            password (str): 应用密码（从坚果云安全选项中创建）
            dav_url (str, optional): WebDAV服务器地址。默认为 "dav.jianguoyun.com/dav"。
        """
        self.auth = (username, password)
        self.dav_url = f"https://{dav_url}"

    def list_documents(self, folder_path: str = "/", max_depth: int = 2) -> List[Dict[str, Any]]:
        """列出指定文件夹中的所有文档（非递归）

        Args:
            folder_path (str, optional): 文件夹路径。默认为根目录 "/"。
            max_depth (int, optional): 最大递归深度。默认为2层。

        Returns:
            List[Dict[str, Any]]: 文档列表，每个文档包含元数据信息
        """
        print(f"列出文件夹: {folder_path} 的内容，最大深度: {max_depth}")
        return self._list_documents_non_recursive(folder_path, max_depth)

    def _list_documents_non_recursive(self, folder_path: str, max_depth: int, current_depth: int = 0) -> List[Dict[str, Any]]:
        """以非递归方式列出指定文件夹中的所有文档

        Args:
            folder_path (str): 文件夹路径
            max_depth (int): 最大递归深度
            current_depth (int, optional): 当前深度。默认为0。

        Returns:
            List[Dict[str, Any]]: 文档列表
        """
        # 检查是否已经到达最大深度
        if current_depth > max_depth:
            print(f"已达到最大深度 {max_depth}，停止递归: {folder_path}")
            return []

        if not folder_path.startswith("/"):
            folder_path = f"/{folder_path}"
        if not folder_path.endswith("/"):
            folder_path = f"{folder_path}/"

        url = f"{self.dav_url}{folder_path}"

        headers = {
            "Depth": "1",
            "Content-Type": "application/xml"
        }

        body = (
            '<?xml version="1.0" encoding="utf-8" ?>\n'
            '<d:propfind xmlns:d="DAV:">\n'
            '  <d:prop>\n'
            '    <d:resourcetype/>\n'
            '    <d:getcontentlength/>\n'
            '    <d:getcontenttype/>\n'
            '    <d:getlastmodified/>\n'
            '    <d:displayname/>\n'
            '  </d:prop>\n'
            '</d:propfind>'
        )

        try:
            response = requests.request(
                "PROPFIND", 
                url, 
                auth=self.auth, 
                headers=headers, 
                data=body
            )

            response.raise_for_status()

            # 解析响应XML
            files_info = self._parse_propfind_response(response.content, folder_path)

            # 获取所有文件和文件夹
            result = []
            folders_to_process = []

            # 跳过当前目录的条目
            for file_info in files_info[1:]:  # 跳过第一个（当前目录）
                if file_info.get("type") == "folder" and current_depth < max_depth:
                    # 将文件夹添加到待处理列表
                    folders_to_process.append(file_info.get("href"))

                result.append(file_info)

            # 处理子文件夹（如果未达到最大深度）
            if current_depth < max_depth:
                for folder_href in folders_to_process:
                    folder_path = unquote(urlparse(folder_href).path.replace(self.dav_url, ""))
                    child_files = self._list_documents_non_recursive(
                        folder_path, 
                        max_depth, 
                        current_depth + 1
                    )
                    result.extend(child_files)

            return result

        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return []

    def _parse_propfind_response(self, xml_content: bytes, base_folder: str) -> List[Dict[str, Any]]:
        """解析PROPFIND响应的XML内容

        Args:
            xml_content (bytes): XML响应内容
            base_folder (str): 基础文件夹路径

        Returns:
            List[Dict[str, Any]]: 文件和文件夹信息列表
        """
        result = []

        try:
            root = ET.fromstring(xml_content)

            # 定义命名空间映射
            ns = {
                'd': 'DAV:',
            }

            # 遍历所有响应
            for response in root.findall('.//d:response', ns):
                href = response.find('./d:href', ns)
                if href is None or not href.text:
                    continue

                href_text = href.text

                # 解析属性
                prop = response.find('./d:propstat/d:prop', ns)
                if prop is None:
                    continue

                # 获取资源类型（文件夹或文件）
                resource_type = prop.find('./d:resourcetype', ns)
                is_folder = resource_type is not None and resource_type.find('./d:collection', ns) is not None

                # 获取文件名/文件夹名
                display_name = prop.find('./d:displayname', ns)
                name = display_name.text if display_name is not None and display_name.text else ''

                # 如果名称为空，从href中提取
                if not name:
                    path = unquote(urlparse(href_text).path)
                    name = os.path.basename(path.rstrip('/'))

                # 获取内容长度（文件大小）
                content_length = prop.find('./d:getcontentlength', ns)
                size = int(content_length.text) if content_length is not None and content_length.text else 0

                # 获取内容类型
                content_type = prop.find('./d:getcontenttype', ns)
                mime_type = content_type.text if content_type is not None and content_type.text else ''

                # 获取最后修改时间
                last_modified = prop.find('./d:getlastmodified', ns)
                modified = last_modified.text if last_modified is not None and last_modified.text else ''

                # 尝试将时间字符串转换为ISO格式
                try:
                    if modified:
                        dt = datetime.strptime(modified, '%a, %d %b %Y %H:%M:%S %Z')
                        modified = dt.isoformat()
                except ValueError:
                    pass

                # 构建结果对象
                file_info = {
                    "href": href_text,
                    "name": name,
                    "type": "folder" if is_folder else "file",
                    "size": size,
                    "mime_type": mime_type,
                    "modified": modified,
                    "path": unquote(urlparse(href_text).path.replace(self.dav_url, ""))
                }

                result.append(file_info)

            return result

        except ET.ParseError as e:
            print(f"XML解析错误: {e}")
            return []

    def get_document_content(self, file_path: str) -> Tuple[Optional[str], str]:
        """获取文档内容

        Args:
            file_path (str): 文档路径

        Returns:
            Tuple[Optional[str], str]: 元组，包含(文档内容, MIME类型)
        """
        if not file_path.startswith("/"):
            file_path = f"/{file_path}"

        url = f"{self.dav_url}{file_path}"

        try:
            response = requests.get(url, auth=self.auth)
            response.raise_for_status()

            mime_type = response.headers.get('Content-Type', '')

            # 如果是文本类型，直接返回内容
            if 'text/' in mime_type or 'application/json' in mime_type:
                return response.text, mime_type

            # 对于二进制文件，返回Base64编码
            return base64.b64encode(response.content).decode('utf-8'), mime_type

        except requests.exceptions.RequestException as e:
            print(f"获取文档内容失败: {e}")
            return None, ""

    def get_documents_with_content(self, folder_path: str = "/", max_depth: int = 2, 
                                  file_types: List[str] = None,
                                  date: str = None, keywords: str = None) -> List[Dict[str, Any]]:
        """获取文档列表及其内容

        Args:
            folder_path (str, optional): 文件夹路径。默认为"/"。
            max_depth (int, optional): 最大递归深度。默认为2。
            file_types (List[str], optional): 文件类型筛选列表。默认为None。
            date (str, optional): 按日期筛选(YYYY-MM-DD)。默认为None。
            keywords (str, optional): 按关键词筛选。默认为None。

        Returns:
            List[Dict[str, Any]]: 文档列表，包含内容
        """
        files = self.list_documents(folder_path, max_depth)

        # 筛选文件（不是文件夹）
        files_in_current_dir = [f for f in files if f['type'] == 'file']

        # 按文件类型筛选
        if file_types:
            files_in_current_dir = [
                item for item in files_in_current_dir
                if any(item['name'].lower().endswith(f'.{ft.lower()}') for ft in file_types)
            ]

        # 按日期和关键词筛选
        if date or keywords:
            filtered_files = []
            for item in files_in_current_dir:
                include_file = True

                # 按日期过滤
                if date and include_file:
                    try:
                        target_date = datetime.strptime(date, "%Y-%m-%d").date()
                        file_date = datetime.fromisoformat(item.get('modified', '')).date()
                        if file_date != target_date:
                            include_file = False
                    except (ValueError, TypeError):
                        include_file = False

                # 按关键词过滤
                if keywords and include_file:
                    kw_list = keywords.lower().split()
                    if not all(kw in item.get('name', '').lower() for kw in kw_list):
                        include_file = False

                if include_file:
                    filtered_files.append(item)

            files_in_current_dir = filtered_files

        # 获取每个文件的内容
        result = []
        for file in files_in_current_dir:
            content, mime_type = self.get_document_content(file['path'])
            file_with_content = file.copy()
            file_with_content['content'] = content
            file_with_content['mime_type'] = mime_type
            result.append(file_with_content)

        return result

def main():
    """主函数：示例如何使用API获取坚果云文档"""

    # 设置认证信息
    username = "<EMAIL>"  # 你的坚果云账户名/邮箱
    password = "an6upcx74sxmqcu8"  # 在坚果云安全选项中创建的应用密码

    # 创建API客户端
    api = NutstoreAPI(username, password)

    # 示例：获取根目录下的所有文档，最大深度为2层
    documents = api.list_documents(folder_path="/", max_depth=2)
    print(f"找到 {len(documents)} 个文档和文件夹")

    # 输出文档列表
    for doc in documents:
        doc_type = "📁" if doc['type'] == "folder" else "📄"
        print(f"{doc_type} {doc['path']} ({doc['size']} bytes, 修改于 {doc['modified']})")

    # 示例：获取文档内容（仅处理.txt和.md文件）
    documents_with_content = api.get_documents_with_content(
        folder_path="/",
        max_depth=2,
        file_types=["txt", "md"],
        # date="2024-06-01",  # 可选：按日期筛选
        # keywords="会议笔记"  # 可选：按关键词筛选
    )

    print(f"\n找到 {len(documents_with_content)} 个文本文档")

    # 将结果保存为JSON文件
    with open("nutstore_documents.json", "w", encoding="utf-8") as f:
        json.dump(documents_with_content, f, ensure_ascii=False, indent=2)

    print("文档内容已保存到 nutstore_documents.json")

if __name__ == "__main__":
    main()
