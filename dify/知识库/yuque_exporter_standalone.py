#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Project ：yuque-export (Standalone Version)
@File    ：yuque_exporter_standalone.py
@desc    ：A single-file script to export Yuque documents.
<AUTHOR> (Original) & Gemini (Refactored)
@Date    ：2024/02/18
"""

import asyncio
import json
import os
import random
import re
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

# 第三方库，需要通过 pip install -r requirements.txt 安装
import aiofiles
import aiohttp
from tqdm import tqdm

# -----------------------------------------------------------------------------
# [整合] config.settings
# -----------------------------------------------------------------------------
# 将原 config/settings.py 的内容整合到此处

# 文件导出路径, 默认为脚本所在目录下的 "output" 文件夹
SAVE_PLACE = "output"

# 每次请求间的休眠时间 (秒), 以防止请求过于频繁被封禁
# 随机休眠 wait_time 到 wait_time + wait_time_delta 之间
IS_WAIT = True
WAIT_TIME = 0.5
WAIT_TIME_DELTA = 0.3

# Markdown 导出参数
# attachment: 是否导出 LaTeX 公式图片
# latexcode: 是否将 LaTeX 公式导出为 Markdown 语法
# anchor: 是否保持语雀的锚点
# linebreak: 是否保持语雀的换行
ATTACHMENT = "true"
LATEXCODE = "false"
ANCHOR = "false"
LINEBREAK = "false"


# -----------------------------------------------------------------------------
# [整合] yuque.utils
# -----------------------------------------------------------------------------
# 将原 yuque/utils.py 的内容整合到此处

def print_colored_text(color_code: int, text: str):
    """打印彩色文字"""
    print(f"\033[{color_code}m{text}\033[0m")


def get_colored_text(color_code: int, text: str) -> str:
    """获取带颜色的文字字符串"""
    return f"\033[{color_code}m{text}\033[0m"


def start_program():
    """打印启动横幅"""
    ascii_art = """
██╗   ██╗██╗   ██╗ ██████╗ ██╗   ██╗███████╗    ███████╗██╗  ██╗██████╗  ██████╗ ██████╗ ████████╗
╚██╗ ██╔╝██║   ██║██╔═══██╗██║   ██║██╔════╝    ██╔════╝╚██╗██╔╝██╔══██╗██╔═══██╗██╔══██╗╚══██╔══╝
 ╚████╔╝ ██║   ██║██║   ██║██║   ██║█████╗█████╗█████╗   ╚███╔╝ ██████╔╝██║   ██║██████╔╝   ██║
  ╚██╔╝  ██║   ██║██║▄▄ ██║██║   ██║██╔══╝╚════╝██╔══╝   ██╔██╗ ██╔═══╝ ██║   ██║██╔══██╗   ██║
   ██║   ╚██████╔╝╚██████╔╝╚██████╔╝███████╗    ███████╗██╔╝ ██╗██║     ╚██████╔╝██║  ██║   ██║
   ╚═╝    ╚═════╝  ╚══▀▀═╝  ╚═════╝ ╚══════╝    ╚══════╝╚═╝  ╚═╝╚═╝      ╚═════╝ ╚═╝  ╚═╝   ╚═╝
                                                                            by Natro92
    """
    print(ascii_art)


# -----------------------------------------------------------------------------
# [整合] yuque.api
# -----------------------------------------------------------------------------
# 将原 yuque/api.py 的内容整合到此处

async def _api_get_request(session: aiohttp.ClientSession, url: str, cookie: str) -> Dict | str:
    """通用的异步GET请求函数"""
    headers = {
        'Host': 'www.yuque.com',
        'Cookie': f'_yuque_session={cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36'
    }
    async with session.get(url, headers=headers) as response:
        response.raise_for_status()
        # 根据Content-Type判断是JSON还是文本
        if 'application/json' in response.headers.get('Content-Type', ''):
            return await response.json()
        return await response.text()


async def get_info(cookie: str, session: aiohttp.ClientSession) -> Dict:
    """获取个人信息"""
    url = 'https://www.yuque.com/api/mine'
    return await _api_get_request(session, url, cookie)


async def get_book_stacks(cookie: str, session: aiohttp.ClientSession) -> Dict:
    """获取知识库列表"""
    url = 'https://www.yuque.com/api/mine/book_stacks'
    return await _api_get_request(session, url, cookie)


async def get_docs(cookie: str, book_id: str, session: aiohttp.ClientSession) -> Dict:
    """获取指定知识库的文档列表"""
    url = f'https://www.yuque.com/api/docs?book_id={book_id}'
    return await _api_get_request(session, url, cookie)


async def get_markdown(cookie: str, session: aiohttp.ClientSession, login: str, book_slug: str, doc_slug: str) -> str:
    """获取单篇文档的Markdown内容"""
    url = (f'https://www.yuque.com/{login}/{book_slug}/{doc_slug}/markdown'
           f'?attachment={ATTACHMENT}&latexcode={LATEXCODE}&anchor={ANCHOR}&linebreak={LINEBREAK}')
    return await _api_get_request(session, url, cookie)


async def write_text_to_markdown(filename: str, text: str):
    """异步写入Markdown文件"""
    file_path = Path(SAVE_PLACE) / filename
    file_path.parent.mkdir(parents=True, exist_ok=True)
    async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
        await f.write(text)
    tqdm.write(get_colored_text(32, f"  > {filename} 保存成功"))


async def export_single_doc(cookie: str, session: aiohttp.ClientSession, login: str, doc_info: Dict):
    """导出单篇文档的核心逻辑"""
    if doc_info['type'] == 'Doc':
        markdown_text = await get_markdown(cookie, session, login, doc_info['book_slug'], doc_info['slug'])
        file_name = f"{doc_info['name']}/{doc_info['title']}.md"
        await write_text_to_markdown(file_name, markdown_text)


async def generate_markdown_tasks(cookie: str, login: str, doc_list: List[Dict]):
    """创建并执行所有文档的导出任务"""
    timeout = aiohttp.ClientTimeout(total=60)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        tasks = []
        for doc in tqdm(doc_list, desc="Exporting Documents"):
            if IS_WAIT:
                await asyncio.sleep(random.uniform(WAIT_TIME, WAIT_TIME + WAIT_TIME_DELTA))
            task = asyncio.create_task(export_single_doc(cookie, session, login, doc))
            tasks.append(task)
        await asyncio.gather(*tasks)
    print_colored_text(32, f"\n[*] 全部导出任务完成！")


# -----------------------------------------------------------------------------
# 主逻辑函数
# -----------------------------------------------------------------------------

async def export_documents(
        cookie: str,
        book_name: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        title_keyword: Optional[str] = None,
        limit: Optional[int] = None
) -> Dict[str, Any]:
    """
    导出指定知识库的文档，并返回结构化的数据。

    :param cookie: 语雀的 _yuque_session cookie 值。
    :param book_name: 要导出的知识库的名称。
    :param start_date: 筛选文档的开始日期 (YYYY-MM-DD)。
    :param end_date: 筛选文档的结束日期 (YYYY-MM-DD)。
    :param title_keyword: 筛选文档的标题关键词。
    :param limit: 限制导出的文档数量。
    :return: 包含导出结果的字典。
    """
    print_colored_text(33, "[*] 正在初始化客户端会话...")
    async with aiohttp.ClientSession() as session:
        try:
            print_colored_text(33, "[*] 正在获取用户信息和知识库列表...")
            info_json, book_stacks_json = await asyncio.gather(
                get_info(cookie, session),
                get_book_stacks(cookie, session)
            )
            login_name = info_json['data']['login']
        except Exception as e:
            print_colored_text(31, f"[!] 获取信息失败: {e}")
            print_colored_text(31, "[!] 请检查Cookie是否正确或网络连接是否正常。")
            return {"status": "error", "message": "Failed to fetch initial data."}

        target_book = next((book for group in book_stacks_json.get('data', []) for book in group.get('books', []) if
                            book['name'] == book_name), None)

        if not target_book:
            all_book_names = [book['name'] for group in book_stacks_json.get('data', []) for book in
                              group.get('books', [])]
            print_colored_text(31, f"[!] 未找到名为 '{book_name}' 的知识库。")
            print_colored_text(33, f"[*] 可用的知识库: {', '.join(all_book_names)}")
            return {"status": "error", "message": f"Knowledge base '{book_name}' not found."}

        print_colored_text(32, f"[*] 已找到知识库: {book_name} (ID: {target_book['id']})")

        print_colored_text(33, "[*] 正在获取文档列表...")
        docs_json = await get_docs(cookie, str(target_book['id']), session)
        all_docs_data = docs_json.get('data', [])
        print_colored_text(32, f"[*] 知识库《{book_name}》共有 {len(all_docs_data)} 篇文档。")

        # 筛选文档
        filtered_docs = all_docs_data
        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            filtered_docs = [d for d in filtered_docs if
                             datetime.fromisoformat(d['created_at'].replace('Z', '+00:00')) >= start_dt]
        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            filtered_docs = [d for d in filtered_docs if
                             datetime.fromisoformat(d['created_at'].replace('Z', '+00:00')) <= end_dt]
        if title_keyword:
            filtered_docs = [d for d in filtered_docs if title_keyword.lower() in d['title'].lower()]
        if limit:
            filtered_docs = sorted(filtered_docs, key=lambda d: d['created_at'], reverse=True)[:limit]

        print_colored_text(32, f"[*] 筛选后将导出 {len(filtered_docs)} 篇文档。")

        if not filtered_docs:
            return {"status": "success", "message": "No documents to export after filtering."}

        doc_list_to_export = [{**doc, "name": target_book['name'], "book_slug": target_book['slug']} for doc in
                              filtered_docs]

    # 单独启动导出任务生成器
    await generate_markdown_tasks(cookie, login_name, doc_list_to_export)

    # 保存结构化数据
    json_path = Path(SAVE_PLACE) / "output.json"
    json_path.parent.mkdir(parents=True, exist_ok=True)
    structured_data = {
        "knowledge_base": book_name,
        "export_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "document_count": len(doc_list_to_export),
        "documents": doc_list_to_export
    }
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(structured_data, f, ensure_ascii=False, indent=4)
    print_colored_text(32, f"\n[*] 结构化数据已保存至 {json_path}")

    return {"status": "success", "data": structured_data}


# -----------------------------------------------------------------------------
# 脚本入口
# -----------------------------------------------------------------------------

async def main():
    """
    主函数，用于配置和调用导出逻辑。
    """
    start_program()

    # --- 请在这里配置您的参数 ---
    # 1. 配置你的Cookie
    # 建议使用环境变量 YUQUE_COOKIE, 或者直接在此处修改
    YUQUE_COOKIE = os.environ.get("YUQUE_COOKIE", "5ZBHoRQuNa9Cir_GW-U-6QlmrrwECY7-34TQxO1RtkrC_qwM1Hg4GbsPnvq62bGWRM8b08L-o8ZbA0k1JJKszQ==")

    if YUQUE_COOKIE == "your_cookie_string_here":
        print_colored_text(31, "[!] 请设置您的YUQUE_COOKIE！")
        print_colored_text(33, "[*] 您可以将其设置为环境变量，或直接在脚本中修改此值。")
        return

    # 2. 配置导出参数
    await export_documents(
        cookie=YUQUE_COOKIE,
        book_name="个人投资总结复盘",  # 替换为你要导出的知识库的准确名称
        # --- 以下为可选的筛选参数，不需要请注释掉或设为 None ---
        # start_date="2023-01-01",
        # end_date="2023-12-31",
        # title_keyword="测试",
        limit=5
    )


if __name__ == '__main__':
    # 确保在 Windows 上 aiohttp 能正常工作
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    asyncio.run(main())