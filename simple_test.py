#!/usr/bin/env python3
"""
简单测试脚本 - 验证数据加载和基本功能
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🧪 开始简单测试")

try:
    print("1. 测试导入模块...")
    import pandas as pd
    print("✅ pandas导入成功")
    
    from app.strategy.services.data_service import DataService
    print("✅ DataService导入成功")
    
    from app.strategy.indicators.parquet_storage import ParquetIndicatorStorage
    print("✅ ParquetIndicatorStorage导入成功")
    
    from app.strategy.indicators.batch_calculator import BatchIndicatorCalculator
    print("✅ BatchIndicatorCalculator导入成功")
    
    print("\n2. 测试数据服务...")
    data_service = DataService()
    
    # 测试获取可用日期
    available_dates = data_service.get_available_dates()
    print(f"✅ 可用日期数量: {len(available_dates)}")
    if available_dates:
        print(f"   最新日期: {available_dates[-1]}")
        print(f"   最早日期: {available_dates[0]}")
    
    # 测试加载最新日期的数据
    if available_dates:
        latest_date = available_dates[-1]
        daily_data = data_service.load_daily_data(latest_date)
        print(f"✅ 最新日期 {latest_date} 数据: {len(daily_data)} 条记录")
        if not daily_data.empty:
            print(f"   股票数量: {daily_data['symbol'].nunique()}")
            print(f"   列名: {list(daily_data.columns)}")
    
    print("\n3. 测试Parquet存储...")
    storage = ParquetIndicatorStorage()
    
    # 检查存储状态
    earliest_date, latest_date = storage.get_date_range()
    symbols = storage.get_available_symbols()
    
    print(f"✅ Parquet存储状态:")
    print(f"   日期范围: {earliest_date} 到 {latest_date}")
    print(f"   股票数量: {len(symbols)}")
    
    print("\n4. 测试批量计算器...")
    calculator = BatchIndicatorCalculator(max_workers=2)
    print(f"✅ 批量计算器初始化成功，工作进程数: {calculator.max_workers}")
    
    print("\n🎉 所有基本测试通过!")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
