<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票详情 - {{ symbol }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        .price-up { color: #dc3545; }
        .price-down { color: #198754; }
        .price-neutral { color: #6c757d; }
        .indicator-card {
            border-left: 4px solid #0d6efd;
            background: #f8f9fa;
        }
        .chart-container {
            height: 500px;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }
        .indicator-value {
            font-size: 1.2rem;
            font-weight: bold;
        }
        .indicator-label {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .time-range-btn {
            margin: 0 2px;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .pattern-item {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
        }
        .pattern-signal-bullish {
            border-left: 4px solid #198754;
            background: #d1e7dd;
        }
        .pattern-signal-bearish {
            border-left: 4px solid #dc3545;
            background: #f8d7da;
        }
        .pattern-signal-neutral {
            border-left: 4px solid #6c757d;
            background: #e2e3e5;
        }
        .pattern-confidence {
            font-size: 0.875rem;
            font-weight: 600;
        }
        .pattern-date {
            font-size: 0.75rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-graph-up"></i> 量化分析系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/quantitative/stocks">股票列表</a>
                <a class="nav-link" href="/quantitative/indicators">指标排行</a>
                <a class="nav-link" href="/quantitative/patterns">形态筛选</a>
                <a class="nav-link" href="/quantitative/market">市场概览</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 股票基本信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h2 class="mb-1" id="stockSymbol">{{ symbol }}</h2>
                                <p class="text-muted mb-0" id="stockName">加载中...</p>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <div class="d-flex justify-content-md-end align-items-center gap-3">
                                    <div class="text-center">
                                        <div class="h3 mb-0" id="currentPrice">-</div>
                                        <small class="text-muted">最新价</small>
                                    </div>
                                    <div class="text-center">
                                        <div class="h4 mb-0" id="priceChange">-</div>
                                        <small class="text-muted">涨跌幅</small>
                                    </div>
                                    <div class="text-center">
                                        <div class="h5 mb-0" id="volume">-</div>
                                        <small class="text-muted">成交量</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- K线图和指标 -->
        <div class="row">
            <div class="col-lg-8">
                <!-- 高性能K线图 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>高性能K线图
                        </h5>
                    </div>
                    <div class="card-body">
                        {% include 'quantitative/components/advanced_kline.html' %}
                        </div>
                    </div>
                </div>

                <!-- 技术指标图表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">技术指标</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="position-relative">
                            <div id="indicatorChart" class="chart-container"></div>
                            <div class="loading-overlay" id="indicatorLoading" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- 实时指标 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">实时指标</h5>
                    </div>
                    <div class="card-body" id="realTimeIndicators">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- K线形态识别 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-graph-up-arrow"></i> K线形态识别
                            </h5>
                            <div class="btn-group btn-group-sm" role="group">
                                <input type="radio" class="btn-check" name="patternView" id="recentPatterns" checked>
                                <label class="btn btn-outline-primary" for="recentPatterns">最新形态</label>

                                <input type="radio" class="btn-check" name="patternView" id="allPatterns">
                                <label class="btn btn-outline-primary" for="allPatterns">全部形态</label>
                            </div>
                        </div>
                    </div>
                    <div class="card-body" id="patternRecognition">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 指标详情 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">指标详情</h5>
                    </div>
                    <div class="card-body" id="indicatorDetails">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        const symbol = '{{ symbol }}';
        let klineChart = null;
        let indicatorChart = null;
        let currentRange = 120; // 默认显示120天

        // 全局变量
        let advancedKlineChart;
        let allPatternsData = null; // 存储所有形态数据
        let showAllPatterns = false; // 是否显示全部形态

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化Lightweight Charts K线图
            advancedKlineChart = initAdvancedKlineChart('{{ symbol }}');
            initCharts();
            loadStockBasicInfo();
            loadStockData();
            loadPatternRecognition();
            setupEventListeners();
        });

        // 初始化图表
        function initCharts() {
            // 只初始化存在的图表
            const indicatorChartElement = document.getElementById('indicatorChart');
            if (indicatorChartElement) {
                indicatorChart = echarts.init(indicatorChartElement);

                // 响应式调整
                window.addEventListener('resize', function() {
                    if (indicatorChart) {
                        indicatorChart.resize();
                    }
                });
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 时间范围按钮
            document.querySelectorAll('.time-range-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.time-range-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentRange = parseInt(this.dataset.range);
                    loadStockData();
                });
            });

            // 形态视图切换
            document.querySelectorAll('input[name="patternView"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    showAllPatterns = this.id === 'allPatterns';
                    if (allPatternsData) {
                        renderPatternRecognition(allPatternsData);
                    }
                });
            });
        }

        // 加载股票基本信息
        async function loadStockBasicInfo() {
            try {
                // 使用K线数据获取最新信息
                const response = await fetch(`/api/quantitative/stocks/${symbol}/kline?limit=1`);
                if (!response.ok) {
                    throw new Error('获取股票信息失败');
                }

                const data = await response.json();
                if (!data.kline || data.kline.length === 0) {
                    throw new Error('股票数据为空');
                }

                const latestData = data.kline[0];

                document.getElementById('stockSymbol').textContent = symbol;
                document.getElementById('stockName').textContent = `股票 ${symbol}`;

                const currentPrice = document.getElementById('currentPrice');
                currentPrice.textContent = '¥' + latestData.close.toFixed(2);

                const priceChange = document.getElementById('priceChange');
                const changeClass = latestData.pct_change > 0 ? 'price-up' :
                                   latestData.pct_change < 0 ? 'price-down' : 'price-neutral';
                priceChange.textContent = latestData.pct_change.toFixed(2) + '%';
                priceChange.className = changeClass;
                currentPrice.className = changeClass;

                document.getElementById('volume').textContent = formatVolume(latestData.volume);

            } catch (error) {
                console.error('加载股票基本信息失败:', error);
                document.getElementById('stockName').textContent = '数据加载失败';
            }
        }

        // 加载股票数据
        async function loadStockData() {
            showChartLoading(true);

            try {
                // 并行加载K线数据和指标数据
                const [klineResponse, indicatorResponse] = await Promise.all([
                    fetch(`/api/quantitative/stocks/${symbol}/kline?limit=${currentRange}`),
                    fetch(`/api/quantitative/stocks/${symbol}/indicators`)
                ]);

                if (!klineResponse.ok || !indicatorResponse.ok) {
                    throw new Error('数据加载失败');
                }

                const klineData = await klineResponse.json();
                const indicatorData = await indicatorResponse.json();

                // 渲染技术指标图表
                renderIndicatorChart(indicatorData.indicators);

                // 渲染实时指标和详情
                renderRealTimeIndicators(klineData.kline, indicatorData.indicators);
                renderIndicatorDetails(indicatorData.indicators);

            } catch (error) {
                console.error('加载股票数据失败:', error);
                showError('加载数据失败，请稍后重试');
            } finally {
                showChartLoading(false);
            }
        }

        // 加载K线形态识别
        async function loadPatternRecognition() {
            const container = document.getElementById('patternRecognition');

            try {
                const response = await fetch(`/api/quantitative/stocks/${symbol}/patterns`);

                if (!response.ok) {
                    throw new Error('获取形态识别数据失败');
                }

                const data = await response.json();

                allPatternsData = data; // 存储数据
                renderPatternRecognition(data);

            } catch (error) {
                console.error('加载形态识别失败:', error);
                container.innerHTML = `
                    <div class="alert alert-warning" role="alert">
                        <i class="bi bi-exclamation-triangle"></i>
                        暂无形态识别数据或加载失败
                    </div>
                `;
            }
        }

        // 渲染形态识别结果
        function renderPatternRecognition(patternData) {
            const container = document.getElementById('patternRecognition');

            if (!patternData || !patternData.patterns || patternData.patterns.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-info" role="alert">
                        <i class="bi bi-info-circle"></i>
                        暂未检测到明显的K线形态
                    </div>
                `;
                return;
            }

            const patterns = patternData.patterns;
            const summary = patternData.summary || {};

            // 按信号类型分组
            const groupedPatterns = {
                'BULLISH': patterns.filter(p => p.signal === 'BULLISH'),
                'BEARISH': patterns.filter(p => p.signal === 'BEARISH'),
                'NEUTRAL': patterns.filter(p => p.signal === 'NEUTRAL')
            };

            let html = '';

            // 显示统计信息
            html += `
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">检测到 ${patterns.length} 个形态</h6>
                            <div class="d-flex gap-2">
                                <span class="badge bg-success">${summary.BULLISH || 0} 看涨</span>
                                <span class="badge bg-danger">${summary.BEARISH || 0} 看跌</span>
                                <span class="badge bg-secondary">${summary.NEUTRAL || 0} 中性</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 根据显示模式决定显示的形态
            let displayPatterns;
            if (showAllPatterns) {
                // 显示全部形态，按日期排序
                displayPatterns = patterns
                    .sort((a, b) => new Date(b.date) - new Date(a.date));
            } else {
                // 只显示最近7天的形态
                const sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

                displayPatterns = patterns
                    .filter(p => new Date(p.date) >= sevenDaysAgo)
                    .sort((a, b) => new Date(b.date) - new Date(a.date))
                    .slice(0, 5); // 最多显示5个最新形态
            }

            // 如果没有符合条件的形态
            if (displayPatterns.length === 0) {
                if (showAllPatterns) {
                    html += `
                        <div class="alert alert-info" role="alert">
                            <i class="bi bi-info-circle"></i>
                            暂未检测到任何K线形态
                        </div>
                    `;
                } else {
                    html += `
                        <div class="alert alert-info" role="alert">
                            <i class="bi bi-info-circle"></i>
                            最近7天内暂无新形态，点击"全部形态"查看历史形态
                        </div>
                    `;
                }
                container.innerHTML = html;
                return;
            }

            html += '<div class="pattern-list">';

            displayPatterns.forEach(pattern => {
                const signalClass = pattern.signal === 'BULLISH' ? 'pattern-signal-bullish' :
                                  pattern.signal === 'BEARISH' ? 'pattern-signal-bearish' : 'pattern-signal-neutral';

                const signalIcon = pattern.signal === 'BULLISH' ? '🟢' :
                                 pattern.signal === 'BEARISH' ? '🔴' : '🟡';

                const signalText = pattern.signal === 'BULLISH' ? '看涨' :
                                 pattern.signal === 'BEARISH' ? '看跌' : '中性';

                html += `
                    <div class="pattern-item ${signalClass}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-1">
                                    <span class="me-2">${signalIcon}</span>
                                    <strong>${pattern.description}</strong>
                                    <span class="badge bg-light text-dark ms-2">${signalText}</span>
                                </div>
                                <div class="pattern-date">${pattern.date}</div>
                            </div>
                            <div class="text-end">
                                <div class="pattern-confidence">
                                    置信度: ${(pattern.confidence * 100).toFixed(0)}%
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';

            // 显示形态统计信息
            if (!showAllPatterns && patterns.length > displayPatterns.length) {
                html += `
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            显示最近 ${displayPatterns.length} 个形态，共检测到 ${patterns.length} 个形态
                        </small>
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        // K线图已由Lightweight Charts组件处理，此函数不再需要

        // 渲染指标图表
        function renderIndicatorChart(indicatorData) {
            if (!indicatorChart || !indicatorData || indicatorData.length === 0) {
                console.log('指标图表未初始化或数据为空');
                return;
            }

            const dates = indicatorData.map(item => item.date);
            const macdData = indicatorData.map(item => item.MACD || null);
            const rsiData = indicatorData.map(item => item.RSI || null);
            
            const option = {
                title: {
                    text: '技术指标',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: ['MACD', 'RSI'],
                    top: 30
                },
                grid: [
                    {
                        left: '10%',
                        right: '8%',
                        top: '15%',
                        height: '35%'
                    },
                    {
                        left: '10%',
                        right: '8%',
                        top: '55%',
                        height: '35%'
                    }
                ],
                xAxis: [
                    {
                        type: 'category',
                        data: dates,
                        gridIndex: 0
                    },
                    {
                        type: 'category',
                        data: dates,
                        gridIndex: 1
                    }
                ],
                yAxis: [
                    {
                        gridIndex: 0,
                        name: 'MACD'
                    },
                    {
                        gridIndex: 1,
                        name: 'RSI',
                        min: 0,
                        max: 100
                    }
                ],
                series: [
                    {
                        name: 'MACD',
                        type: 'line',
                        data: macdData,
                        xAxisIndex: 0,
                        yAxisIndex: 0,
                        smooth: true
                    },
                    {
                        name: 'RSI',
                        type: 'line',
                        data: rsiData,
                        xAxisIndex: 1,
                        yAxisIndex: 1,
                        smooth: true,
                        markLine: {
                            data: [
                                { yAxis: 70, name: '超买线' },
                                { yAxis: 30, name: '超卖线' }
                            ]
                        }
                    }
                ]
            };
            
            indicatorChart.setOption(option);
        }

        // 渲染实时指标
        function renderRealTimeIndicators(klineData, indicatorData) {
            const container = document.getElementById('realTimeIndicators');

            if (!klineData || klineData.length === 0 || !indicatorData || indicatorData.length === 0) {
                container.innerHTML = '<p class="text-muted">暂无指标数据</p>';
                return;
            }

            // 获取最新数据
            const latestKline = klineData[0];
            const latestIndicator = indicatorData[0];

            let html = '';

            // 价格信息
            const priceChangeClass = latestKline.pct_change > 0 ? 'text-danger' :
                                   latestKline.pct_change < 0 ? 'text-success' : 'text-muted';
            html += `
                <div class="indicator-card p-3 mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="indicator-label">最新价格</div>
                            <div class="indicator-value ${priceChangeClass}">¥${latestKline.close.toFixed(2)}</div>
                        </div>
                        <div class="text-end">
                            <div class="indicator-label">涨跌幅</div>
                            <div class="indicator-value ${priceChangeClass}">${latestKline.pct_change.toFixed(2)}%</div>
                        </div>
                    </div>
                </div>
            `;

            // RSI指标
            if (latestIndicator.RSI !== null && latestIndicator.RSI !== undefined) {
                const rsi = latestIndicator.RSI;
                const levelClass = rsi > 70 ? 'text-danger' : rsi < 30 ? 'text-success' : 'text-primary';
                const level = rsi > 70 ? '超买' : rsi < 30 ? '超卖' : '正常';
                const badgeClass = rsi > 70 ? 'bg-danger' : rsi < 30 ? 'bg-success' : 'bg-secondary';

                html += `
                    <div class="indicator-card p-3 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="indicator-label">RSI</div>
                                <div class="indicator-value ${levelClass}">${rsi.toFixed(2)}</div>
                            </div>
                            <div class="text-end">
                                <span class="badge ${badgeClass}">${level}</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            // MACD指标
            if (latestIndicator.MACD !== null && latestIndicator.MACD !== undefined) {
                const macd = latestIndicator.MACD;
                const trendClass = macd > 0 ? 'text-success' : 'text-danger';
                const trend = macd > 0 ? '多头' : '空头';
                const badgeClass = macd > 0 ? 'bg-success' : 'bg-danger';

                html += `
                    <div class="indicator-card p-3 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="indicator-label">MACD</div>
                                <div class="indicator-value ${trendClass}">${macd.toFixed(4)}</div>
                            </div>
                            <div class="text-end">
                                <span class="badge ${badgeClass}">${trend}</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            container.innerHTML = html || '<p class="text-muted">暂无指标数据</p>';
        }

        // 渲染指标详情
        function renderIndicatorDetails(indicatorData) {
            const container = document.getElementById('indicatorDetails');

            if (!indicatorData || indicatorData.length === 0) {
                container.innerHTML = '<p class="text-muted">暂无详细数据</p>';
                return;
            }

            const latestIndicator = indicatorData[0];
            let html = '';

            // 均线指标
            html += `
                <div class="mb-3">
                    <h6>移动平均线</h6>
                    <div class="row">
                        <div class="col-6 text-center">
                            <small class="text-muted">EMA5</small>
                            <div class="fw-bold">${latestIndicator.EMA_5 ? latestIndicator.EMA_5.toFixed(2) : '-'}</div>
                        </div>
                        <div class="col-6 text-center">
                            <small class="text-muted">EMA10</small>
                            <div class="fw-bold">${latestIndicator.EMA_10 ? latestIndicator.EMA_10.toFixed(2) : '-'}</div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-6 text-center">
                            <small class="text-muted">EMA20</small>
                            <div class="fw-bold">${latestIndicator.EMA_20 ? latestIndicator.EMA_20.toFixed(2) : '-'}</div>
                        </div>
                        <div class="col-6 text-center">
                            <small class="text-muted">EMA60</small>
                            <div class="fw-bold">${latestIndicator.EMA_60 ? latestIndicator.EMA_60.toFixed(2) : '-'}</div>
                        </div>
                    </div>
                </div>
            `;

            // KDJ指标
            if (latestIndicator.KDJ_K !== null || latestIndicator.KDJ_D !== null || latestIndicator.KDJ_J !== null) {
                html += `
                    <div class="mb-3">
                        <h6>KDJ指标</h6>
                        <div class="row">
                            <div class="col-4 text-center">
                                <small class="text-muted">K值</small>
                                <div class="fw-bold">${latestIndicator.KDJ_K ? latestIndicator.KDJ_K.toFixed(2) : '-'}</div>
                            </div>
                            <div class="col-4 text-center">
                                <small class="text-muted">D值</small>
                                <div class="fw-bold">${latestIndicator.KDJ_D ? latestIndicator.KDJ_D.toFixed(2) : '-'}</div>
                            </div>
                            <div class="col-4 text-center">
                                <small class="text-muted">J值</small>
                                <div class="fw-bold">${latestIndicator.KDJ_J ? latestIndicator.KDJ_J.toFixed(2) : '-'}</div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // 布林带指标
            if (latestIndicator.BOLL_Upper !== null || latestIndicator.BOLL_Middle !== null || latestIndicator.BOLL_Lower !== null) {
                html += `
                    <div class="mb-3">
                        <h6>布林带</h6>
                        <div class="row">
                            <div class="col-4 text-center">
                                <small class="text-muted">上轨</small>
                                <div class="fw-bold">${latestIndicator.BOLL_Upper ? latestIndicator.BOLL_Upper.toFixed(2) : '-'}</div>
                            </div>
                            <div class="col-4 text-center">
                                <small class="text-muted">中轨</small>
                                <div class="fw-bold">${latestIndicator.BOLL_Middle ? latestIndicator.BOLL_Middle.toFixed(2) : '-'}</div>
                            </div>
                            <div class="col-4 text-center">
                                <small class="text-muted">下轨</small>
                                <div class="fw-bold">${latestIndicator.BOLL_Lower ? latestIndicator.BOLL_Lower.toFixed(2) : '-'}</div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // MACD详情
            if (latestIndicator.MACD !== null || latestIndicator.MACD_Signal !== null || latestIndicator.MACD_Histogram !== null) {
                html += `
                    <div class="mb-3">
                        <h6>MACD详情</h6>
                        <div class="row">
                            <div class="col-4 text-center">
                                <small class="text-muted">MACD</small>
                                <div class="fw-bold">${latestIndicator.MACD ? latestIndicator.MACD.toFixed(4) : '-'}</div>
                            </div>
                            <div class="col-4 text-center">
                                <small class="text-muted">信号线</small>
                                <div class="fw-bold">${latestIndicator.MACD_Signal ? latestIndicator.MACD_Signal.toFixed(4) : '-'}</div>
                            </div>
                            <div class="col-4 text-center">
                                <small class="text-muted">柱状图</small>
                                <div class="fw-bold">${latestIndicator.MACD_Histogram ? latestIndicator.MACD_Histogram.toFixed(4) : '-'}</div>
                            </div>
                        </div>
                    </div>
                `;
            }

            container.innerHTML = html || '<p class="text-muted">暂无详细数据</p>';
        }

        // 显示/隐藏图表加载状态
        function showChartLoading(show) {
            const chartLoading = document.getElementById('chartLoading');
            const indicatorLoading = document.getElementById('indicatorLoading');

            if (chartLoading) {
                chartLoading.style.display = show ? 'flex' : 'none';
            }
            if (indicatorLoading) {
                indicatorLoading.style.display = show ? 'flex' : 'none';
            }
        }

        // 显示错误信息
        function showError(message) {
            console.error(message);
            // 这里可以添加错误提示UI
        }

        // 格式化成交量
        function formatVolume(volume) {
            if (volume >= 100000000) {
                return (volume / 100000000).toFixed(1) + '亿';
            } else if (volume >= 10000) {
                return (volume / 10000).toFixed(1) + '万';
            } else {
                return volume.toFixed(0);
            }
        }
    </script>
</body>
</html>
