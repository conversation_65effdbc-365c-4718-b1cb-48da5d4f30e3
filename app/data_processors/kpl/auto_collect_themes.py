#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦题材自动采集脚本
功能：
1. 分析指定目录中的theme文件，获取最大ID
2. 基于最大ID进行自动采集
3. 支持可配置的采集量参数
4. 按照创建时间组织文件存储
"""

import os
import re
import json
import argparse
import logging
import asyncio
from datetime import datetime
from typing import List, Optional, Dict, Any
from app.config import DataConfig

# 导入开盘啦API
try:
    from dify.kaipanla import KaiPanLaAPI
except ImportError:
    import sys
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
    from dify.kaipanla import KaiPanLaAPI

# 配置日志
from app.utils.logger import get_logger

# 获取当前模块的logger
logger = get_logger(__name__)


def list_all_files_recursive(directory_path):
    """递归列出目录下所有文件"""
    all_files = []
    for entry_name in os.listdir(directory_path):
        full_path = os.path.join(directory_path, entry_name)
        if os.path.isfile(full_path):
            all_files.append(full_path)
        elif os.path.isdir(full_path):
            # 递归调用自身来处理子目录
            all_files.extend(list_all_files_recursive(full_path))
    return all_files


class AutoThemeCollector:
    """自动题材数据采集器"""
    
    def __init__(self, theme_dir: Optional[str] = None):
        """
        初始化自动采集器
        
        Args:
            theme_dir: 题材文件存储目录，如果为None则使用配置中的目录
        """
        self.theme_dir = theme_dir if theme_dir else os.path.join(DataConfig.get_theme_data_dir(), 'themes')
        # 确保目录存在
        os.makedirs(self.theme_dir, exist_ok=True)
        self.api = KaiPanLaAPI()
        logger.info(f"自动题材采集器初始化完成，数据目录: {self.theme_dir}")

    def get_max_theme_id(self) -> int:
        """
        从目录中的文件名获取最大题材ID
        
        Returns:
            最大题材ID，如果没有找到文件则返回默认值0
        """
        if not os.path.exists(self.theme_dir):
            logger.warning(f"目录 {self.theme_dir} 不存在，使用默认ID: 0")
            return 0
        
        # 获取目录中的所有文件（递归搜索）
        all_files = list_all_files_recursive(self.theme_dir)
        
        # 过滤出theme开头的文件，并提取文件名
        theme_files = []
        for file_path in all_files:
            filename = os.path.basename(file_path)
            if filename.startswith('theme_') and filename.endswith('.json'):
                theme_files.append(filename)
        
        if not theme_files:
            logger.warning(f"目录 {self.theme_dir} 中没有找到theme文件，使用默认ID: 0")
            return 0
        
        # 提取ID
        max_id = 0
        for file in theme_files:
            # 使用正则表达式提取ID: theme_数字_名称.json
            match = re.match(r'theme_(\d+)_', file)
            if match:
                file_id = int(match.group(1))
                max_id = max(max_id, file_id)
        
        logger.info(f"从目录中找到最大题材ID: {max_id}")
        return max_id
    
    async def collect_themes(self, start_id: Optional[int] = None, 
                           collect_count: int = 30, step: int = 1) -> dict:
        """
        自动采集题材数据
        
        Args:
            start_id: 起始ID，如果为None则基于最大ID
            collect_count: 采集数量，默认30
            step: 步长，默认为1，可以设置较大值以跳过一些ID
            
        Returns:
            采集结果字典
        """
        # 如果没有指定起始ID，则基于最大ID
        if start_id is None:
            max_id = self.get_max_theme_id()
            start_id = max_id + 1
            logger.info(f"基于最大ID {max_id}，从 {start_id} 开始采集")
        else:
            logger.info(f"使用指定起始ID: {start_id}")
        
        end_id = start_id + collect_count * step
        logger.info(f"采集范围: {start_id} - {end_id} (步长: {step}, 共计算 {collect_count} 个ID)")
        
        # 结果统计
        results = {
            'success': [],
            'empty': [],
            'errors': [],
            'stats': {
                'success_count': 0,
                'empty_count': 0,
                'error_count': 0
            }
        }
        
        processed_count = 0
        consecutive_empty = 0
        max_consecutive_empty = 20  # 如果连续20个ID都是空的，可能已经到达数据末尾
        
        # 使用步长循环处理ID
        for theme_id in range(start_id, end_id, step):
            try:
                # 检查是否已存在
                theme_prefix = f'theme_{theme_id}_'
                existing_file = None
                
                # 递归查找目录中是否已存在该ID的文件
                for root, dirs, files in os.walk(self.theme_dir):
                    for file in files:
                        if file.startswith(theme_prefix) and file.endswith('.json'):
                            existing_file = os.path.join(root, file)
                            break
                    if existing_file:
                        break
                
                if existing_file:
                    logger.info(f"题材ID {theme_id} 已存在: {existing_file}，跳过")
                    consecutive_empty = 0  # 重置连续为空计数
                    processed_count += 1
                    continue
                
                # 获取题材详情
                logger.info(f"正在获取题材ID: {theme_id}")
                theme_data = await self.api.get_theme_detail(str(theme_id))
                
                # 检查名称是否为空
                theme_name = theme_data.get('Name', '').strip()
                if not theme_name:
                    logger.info(f"题材ID {theme_id} 的Name为空，可能不存在")
                    results['empty'].append({
                        'id': theme_id
                    })
                    results['stats']['empty_count'] += 1
                    consecutive_empty += 1
                    
                    # 如果连续多个为空，可能已到达数据末尾
                    if consecutive_empty >= max_consecutive_empty:
                        logger.warning(f"连续 {consecutive_empty} 个ID为空，可能已到达数据末尾，停止采集")
                        break
                    
                    continue
                
                # 重置连续为空计数
                consecutive_empty = 0
                
                # 从CreateTime提取日期
                create_time = int(theme_data.get('CreateTime', '0'))
                if create_time > 0:
                    date_str = datetime.fromtimestamp(create_time).strftime('%Y-%m-%d')
                else:
                    date_str = datetime.now().strftime('%Y-%m-%d')
                
                # 创建日期目录
                date_dir = os.path.join(self.theme_dir, date_str)
                os.makedirs(date_dir, exist_ok=True)
                
                # 保存文件
                # 替换文件名中的非法字符
                safe_name = "".join(c for c in theme_name if c.isalnum() or c in ('_', '-'))
                filename = f'theme_{theme_id}_{safe_name}.json'
                file_path = os.path.join(date_dir, filename)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(theme_data, f, ensure_ascii=False, indent=2)
                
                logger.info(f"成功保存题材: {theme_id} - {theme_name} 到 {file_path}")
                results['success'].append({
                    'id': theme_id,
                    'name': theme_name,
                    'path': file_path
                })
                results['stats']['success_count'] += 1
                
                # 添加延迟，避免请求过快
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"采集题材ID {theme_id} 出错: {str(e)}")
                results['errors'].append({
                    'id': theme_id,
                    'error': str(e)
                })
                results['stats']['error_count'] += 1
            
            processed_count += 1
            
            # 如果已处理足够数量，停止采集
            if processed_count >= collect_count:
                logger.info(f"已完成预定采集量 {collect_count} 个，停止采集")
                break
        
        return results
    
    def get_collection_status(self) -> dict:
        """
        获取当前采集状态信息
        
        Returns:
            状态信息字典
        """
        max_id = self.get_max_theme_id()
        
        if not os.path.exists(self.theme_dir):
            return {
                'theme_dir': self.theme_dir,
                'exists': False,
                'max_id': max_id,
                'file_count': 0
            }
        
        # 递归获取所有文件
        all_files = list_all_files_recursive(self.theme_dir)
        
        # 过滤出theme开头的文件
        theme_files = []
        for file_path in all_files:
            filename = os.path.basename(file_path)
            if filename.startswith('theme_') and filename.endswith('.json'):
                theme_files.append(filename)
        
        return {
            'theme_dir': self.theme_dir,
            'exists': True,
            'max_id': max_id,
            'file_count': len(theme_files),
            'next_start_id': max_id + 1
        }


async def main_async():
    """异步主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='开盘啦题材自动采集脚本')
    parser.add_argument('--dir', type=str, default='/Users/<USER>/PycharmProjects/own/data/kpl_theme/themes',
                       help='题材文件存储目录 (默认: data/kpl_themes)')
    parser.add_argument('--start', type=int, default=None,
                       help='起始题材ID (默认: 自动获取最大ID)')
    parser.add_argument('--count', type=int, default=100,
                       help='采集数量 (默认: 30)')
    parser.add_argument('--step', type=int, default=1,
                       help='采集步长 (默认: 1)')
    args = parser.parse_args()
    
    # 创建采集器并执行采集
    collector = AutoThemeCollector(args.dir)
    results = await collector.collect_themes(args.start, args.count, args.step)
    
    logger.info(f"采集完成，共成功保存 {results['stats']['success_count']} 个题材，"
               f"空数据 {results['stats']['empty_count']} 个，"
               f"错误 {results['stats']['error_count']} 个")

def main():
    """主函数"""
    asyncio.run(main_async())

if __name__ == "__main__":
    main() 