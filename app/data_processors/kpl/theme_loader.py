#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
题材数据加载器
负责从文件系统加载题材数据，并提供查询、筛选等功能
"""

import os
import json
import glob
from typing import List, Dict, Optional, Set
from datetime import datetime
import logging
from collections import defaultdict

from app.config import DataConfig
from app.data_processors.kpl.theme_model import Theme, StockInfo, TableItem, StockListItem

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ThemeLoader:
    """题材数据加载器，负责从文件系统加载题材数据"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """初始化题材加载器"""
        self.data_dir = data_dir if data_dir else os.path.join(DataConfig.get_theme_data_dir(), 'themes')
        self.themes: List[Theme] = []
        self.themes_by_id: Dict[str, Theme] = {}
        self.themes_by_date: Dict[str, List[Theme]] = {}
        self.themes_by_stock: Dict[str, List[Theme]] = {}
        self.stock_info: Dict[str, StockInfo] = {}  # 股票代码 -> 股票信息
        logger.info(f"初始化题材加载器，数据目录: {self.data_dir}")
        self.load_data()
    
    def load_data(self):
        """加载所有题材数据"""
        logger.info("开始重新加载题材数据...")
        self.themes = []
        self.themes_by_id = {}
        self.themes_by_date = defaultdict(list)
        self.themes_by_stock = defaultdict(list)
        self.stock_info = {}
        
        if not os.path.exists(self.data_dir):
            logger.warning(f"题材数据目录 {self.data_dir} 不存在")
            return
            
        logger.info(f"开始加载题材数据，目录: {self.data_dir}")
        loaded_files = 0
        total_stocks = 0
        
        # 递归遍历所有日期目录
        for date_dir in glob.glob(os.path.join(self.data_dir, "*")):
            if os.path.isdir(date_dir):
                # 遍历目录下所有json文件
                date_name = os.path.basename(date_dir)
                logger.debug(f"加载日期目录: {date_name}")
                
                for file_path in glob.glob(os.path.join(date_dir, "theme_*.json")):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            theme = Theme.from_dict(data)
                            
                            # 添加到集合中
                            self.themes.append(theme)
                            self.themes_by_id[theme.id] = theme
                            self.themes_by_date[theme.date].append(theme)
                            
                            # 记录股票与题材的关系
                            theme_stock_count = 0
                            for stock in theme.all_stocks:  # 使用all_stocks获取所有股票
                                if stock.stock_id:  # 确保股票代码不为空
                                    self.themes_by_stock[stock.stock_id].append(theme)
                                    # 更新股票信息
                                    self.stock_info[stock.stock_id] = stock
                                    theme_stock_count += 1
                            
                            loaded_files += 1
                            total_stocks += theme_stock_count
                            logger.debug(f"加载题材: {theme.name} (ID: {theme.id})，股票数: {theme_stock_count}")
                                
                    except Exception as e:
                        logger.error(f"加载题材文件 {file_path} 出错: {e}")
        
        # 检查单独的JSON文件
        for json_file in glob.glob(os.path.join(self.data_dir, "*.json")):
            try:
                if "theme_" in os.path.basename(json_file):
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        theme = Theme.from_dict(data)
                        
                        # 添加到集合中
                        self.themes.append(theme)
                        self.themes_by_id[theme.id] = theme
                        self.themes_by_date[theme.date].append(theme)
                        
                        # 记录股票与题材的关系
                        theme_stock_count = 0
                        for stock in theme.all_stocks:  # 使用all_stocks获取所有股票
                            if stock.stock_id:  # 确保股票代码不为空
                                self.themes_by_stock[stock.stock_id].append(theme)
                                # 更新股票信息
                                self.stock_info[stock.stock_id] = stock
                                theme_stock_count += 1
                        
                        loaded_files += 1
                        total_stocks += theme_stock_count
                        logger.debug(f"加载题材: {theme.name} (ID: {theme.id})，股票数: {theme_stock_count}")
            except Exception as e:
                logger.error(f"加载题材文件 {json_file} 出错: {e}")
                
        # 尝试加载app/data目录下的题材文件
        app_data_dir = os.path.join('app', 'data')
        if os.path.exists(app_data_dir):
            for json_file in glob.glob(os.path.join(app_data_dir, "*.json")):
                try:
                    if "theme_" in os.path.basename(json_file):
                        with open(json_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            theme = Theme.from_dict(data)
                            
                            # 添加到集合中，不重复添加
                            if theme.id not in self.themes_by_id:
                                self.themes.append(theme)
                                self.themes_by_id[theme.id] = theme
                                self.themes_by_date[theme.date].append(theme)
                                
                                # 记录股票与题材的关系
                                theme_stock_count = 0
                                for stock in theme.all_stocks:  # 使用all_stocks获取所有股票
                                    if stock.stock_id:  # 确保股票代码不为空
                                        self.themes_by_stock[stock.stock_id].append(theme)
                                        # 更新股票信息
                                        self.stock_info[stock.stock_id] = stock
                                        theme_stock_count += 1
                                
                                loaded_files += 1
                                total_stocks += theme_stock_count
                                logger.debug(f"加载题材: {theme.name} (ID: {theme.id})，股票数: {theme_stock_count}")
                except Exception as e:
                    logger.error(f"加载题材文件 {json_file} 出错: {e}")
        
        # 按时间排序
        self.themes.sort(key=lambda x: x.create_time, reverse=True)
        
        logger.info(f"题材数据加载完成，共加载文件: {loaded_files} 个，"
                   f"题材总数: {len(self.themes)}，"
                   f"日期总数: {len(self.themes_by_date)}，"
                   f"相关股票总数: {len(self.stock_info)}，"
                   f"股票关联总数: {total_stocks}")
    
    def get_themes(self, 
                 page: int = 1, 
                 page_size: int = 10,
                 date: Optional[str] = None,
                 stock_id: Optional[str] = None) -> List[Theme]:
        """
        获取题材列表，支持分页和筛选
        
        Args:
            page: 页码，从1开始
            page_size: 每页数量
            date: 按日期筛选
            stock_id: 按股票代码筛选
            
        Returns:
            题材列表
        """
        filtered_themes = self.themes
        
        # 按日期筛选
        if date:
            filtered_themes = self.themes_by_date.get(date, [])
        
        # 按股票代码筛选
        if stock_id:
            if date:
                # 如果已经按日期筛选，则在筛选结果中继续筛选
                filtered_themes = [
                    theme for theme in filtered_themes 
                    if stock_id in theme.stock_ids
                ]
            else:
                # 否则直接从索引获取
                filtered_themes = self.themes_by_stock.get(stock_id, [])
        
        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        return filtered_themes[start:end]
    
    def get_theme_by_id(self, theme_id: str) -> Optional[Theme]:
        """根据ID获取题材"""
        theme = self.themes_by_id.get(theme_id)
        if theme:
            logger.debug(f"找到题材 {theme_id}: {theme.name}，股票数: {len(theme.all_stocks)}")
            # 打印层级结构信息
            if theme.tables:
                logger.debug(f"题材 {theme_id} 的层级结构:")
                for i, table in enumerate(theme.tables):
                    logger.debug(f"  表格 {i+1}: {table.level1.name}")
                    for j, level2 in enumerate(table.level2):
                        logger.debug(f"    子分类 {j+1}: {level2.name}，股票数: {len(level2.stocks)}")
        else:
            logger.warning(f"未找到题材 {theme_id}")
        return theme
    
    def get_all_dates(self) -> List[str]:
        """获取所有日期"""
        return sorted(list(self.themes_by_date.keys()), reverse=True)
    
    def get_all_stock_ids(self) -> List[str]:
        """获取所有股票代码"""
        return sorted(list(self.stock_info.keys()))
    
    def get_stock_info(self, stock_id: str) -> Optional[StockInfo]:
        """获取股票信息"""
        return self.stock_info.get(stock_id)
    
    def get_total_count(self,
                      date: Optional[str] = None,
                      stock_id: Optional[str] = None) -> int:
        """
        获取符合条件的题材总数
        
        Args:
            date: 按日期筛选
            stock_id: 按股票代码筛选
            
        Returns:
            题材总数
        """
        if date and stock_id:
            return len([
                theme for theme in self.themes_by_date.get(date, []) 
                if stock_id in theme.stock_ids
            ])
        elif date:
            return len(self.themes_by_date.get(date, []))
        elif stock_id:
            return len(self.themes_by_stock.get(stock_id, []))
        else:
            return len(self.themes)
    
    def get_hot_stocks(self, limit: int = 20) -> List[Dict]:
        """
        获取热门股票列表
        
        Args:
            limit: 返回数量限制
            
        Returns:
            热门股票列表，包含股票代码、名称、关联题材数量
        """
        stock_theme_count = {
            stock_id: len(themes) 
            for stock_id, themes in self.themes_by_stock.items()
        }
        
        # 按关联题材数量排序
        sorted_stocks = sorted(
            stock_theme_count.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        
        # 构造结果
        result = []
        for stock_id, count in sorted_stocks[:limit]:
            stock_info = self.stock_info.get(stock_id)
            if stock_info:
                result.append({
                    'stock_id': stock_id,
                    'name': stock_info.prod_name,
                    'theme_count': count
                })
        
        return result
    
    def get_theme_stats(self) -> Dict:
        """
        获取题材统计数据
        
        Returns:
            包含多种统计信息的字典
        """
        # 计算每日题材数量
        daily_theme_count = {
            date: len(themes) 
            for date, themes in self.themes_by_date.items()
        }
        
        # 计算核心股统计
        core_stock_count = defaultdict(int)
        for theme in self.themes:
            for stock in theme.core_stocks:
                core_stock_count[stock.stock_id] += 1
        
        # 获取核心股排名前20
        top_core_stocks = sorted(
            core_stock_count.items(),
            key=lambda x: x[1],
            reverse=True
        )[:20]
        
        top_core_stocks_with_name = [
            {
                'stock_id': stock_id,
                'name': self.stock_info.get(stock_id).prod_name if self.stock_info.get(stock_id) else '',
                'count': count
            }
            for stock_id, count in top_core_stocks
        ]
        
        return {
            'total_themes': len(self.themes),
            'total_dates': len(self.themes_by_date),
            'total_stocks': len(self.stock_info),
            'daily_theme_count': daily_theme_count,
            'top_core_stocks': top_core_stocks_with_name
        }
    
    def search_themes(self, keyword: str) -> List[Theme]:
        """
        按关键词搜索题材
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的题材列表
        """
        if not keyword:
            return []
            
        keyword = keyword.lower()
        results = []
        
        for theme in self.themes:
            # 搜索名称和简介
            if (keyword in theme.name.lower() or
                keyword in theme.brief_intro.lower()):
                results.append(theme)
                continue
                
            # 搜索股票
            for stock in theme.stocks:
                if (keyword in stock.stock_id.lower() or
                    keyword in stock.prod_name.lower() or
                    keyword in stock.reason.lower()):
                    results.append(theme)
                    break
                    
            # 搜索表格数据
            if not theme in results and theme.tables:
                for table in theme.tables:
                    if self._search_table_item(table, keyword):
                        results.append(theme)
                        break
        
        return results
    
    def _search_table_item(self, table_item: TableItem, keyword: str) -> bool:
        """搜索表格项"""
        # 搜索一级分类
        if keyword in table_item.level1.name.lower():
            return True

        # 搜索二级分类
        if table_item.level2:
            for level2_item in table_item.level2:
                if keyword in level2_item.name.lower():
                    return True

        return False
    
if __name__ == "__main__":
    # 测试代码
    loader = ThemeLoader()
    print(f"加载了 {len(loader.themes)} 个题材数据")
    
    # 获取最近的题材
    recent_themes = loader.get_themes(page=1, page_size=5)
    for theme in recent_themes:
        print(f"ID: {theme.id}, 名称: {theme.name}, 日期: {theme.date}, "
              f"相关股票数: {len(theme.stocks)}")
    
    # 获取统计信息
    stats = loader.get_theme_stats()
    print(f"题材总数: {stats['total_themes']}")
    print(f"日期总数: {stats['total_dates']}")
    print(f"股票总数: {stats['total_stocks']}")
    
    # 获取热门股票
    hot_stocks = loader.get_hot_stocks(5)
    print("热门股票:")
    for stock in hot_stocks:
        print(f"  {stock['stock_id']} {stock['name']}: {stock['theme_count']} 个题材") 