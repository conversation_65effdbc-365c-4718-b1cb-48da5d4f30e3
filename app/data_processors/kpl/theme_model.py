#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
题材数据模型定义
定义题材数据的数据结构，便于后续分析和处理
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

@dataclass
class StockInfo:
    """题材相关股票信息"""
    stock_id: str  # 股票代码
    is_zz: str  # 是否是核心股
    is_hot: str  # 是否是热门股
    reason: str  # 关联原因
    prod_name: str  # 股票名称
    hot: int = 0  # 热度值
    first_shelve_time: int = 0  # 上架时间
    update_cache_time: int = 0  # 更新缓存时间
    is_new: int = 0  # 是否为新股
    tags: List[str] = field(default_factory=list)  # 标签

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StockInfo':
        """从字典创建对象"""
        return cls(
            stock_id=data.get('StockID', ''),
            is_zz=data.get('IsZz', '0'),
            is_hot=data.get('IsHot', '0'),
            reason=data.get('Reason', ''),
            prod_name=data.get('prod_name', ''),
            hot=int(data.get('Hot', 0)),
            first_shelve_time=int(data.get('FirstShelveTime', 0)),
            update_cache_time=int(data.get('UpdateCacheTime', 0)),
            is_new=int(data.get('IsNew', 0)),
            tags=data.get('Tag', [])
        )

@dataclass
class TableLevelItem:
    """题材表格层级项"""
    id: str  # ID
    name: str  # 名称
    zs_code: str = "0"  # 指数代码
    first_shelve_time: int = 0  # 上架时间
    update_cache_time: int = 0  # 更新缓存时间
    is_new: int = 0  # 是否为新股
    stocks: List[StockInfo] = field(default_factory=list)  # 股票列表

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TableLevelItem':
        """从字典创建对象"""
        stocks = []
        if 'Stocks' in data and isinstance(data['Stocks'], list):
            stocks = [StockInfo.from_dict(stock) for stock in data['Stocks']]
            
        return cls(
            id=data.get('ID', ''),
            name=data.get('Name', ''),
            zs_code=data.get('ZSCode', '0'),
            first_shelve_time=int(data.get('FirstShelveTime', 0)),
            update_cache_time=int(data.get('UpdateCacheTime', 0)),
            is_new=int(data.get('IsNew', 0)),
            stocks=stocks
        )

@dataclass
class TableItem:
    """题材表格项"""
    level1: TableLevelItem  # 一级分类
    level2: List[TableLevelItem] = field(default_factory=list)  # 二级分类
    level3: List[Dict[str, Any]] = field(default_factory=list)  # 三级分类（如果有）

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TableItem':
        """从字典创建对象"""
        level1 = TableLevelItem.from_dict(data.get('Level1', {}))
        
        level2 = []
        if 'Level2' in data and isinstance(data['Level2'], list):
            level2 = [TableLevelItem.from_dict(item) for item in data['Level2']]
            
        level3 = []
        if 'Level3' in data and isinstance(data['Level3'], list):
            level3 = data['Level3']
            
        return cls(
            level1=level1,
            level2=level2,
            level3=level3
        )

@dataclass
class StockListItem:
    """股票列表项"""
    stock_id: str  # 股票代码
    prod_name: str  # 股票名称
    hot_num: int = 0  # 热度值
    tags: List[Dict[str, str]] = field(default_factory=list)  # 标签

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StockListItem':
        """从字典创建对象"""
        return cls(
            stock_id=data.get('StockID', ''),
            prod_name=data.get('prod_name', ''),
            hot_num=int(data.get('HotNum', 0)),
            tags=data.get('Tag', [])
        )

@dataclass
class Theme:
    """题材数据模型"""
    id: str  # 题材ID
    name: str  # 题材名称
    brief_intro: str  # 简介
    introduction: str  # 详细介绍
    create_time: int  # 创建时间戳
    update_time: int  # 更新时间戳
    stocks: List[StockInfo]  # 相关股票列表
    class_layer: str = ""  # 分类层级
    desc: str = ""  # 描述
    plate_switch: str = ""  # 板块开关
    stk_switch: str = ""  # 股票开关
    power: int = 0  # 势能
    tables: List[TableItem] = field(default_factory=list)  # 表格数据
    stock_list: List[StockListItem] = field(default_factory=list)  # 股票列表
    good_num: int = 0  # 点赞数
    com_num: int = 0  # 评论数
    is_good: int = 0  # 是否点赞
    zt_list: List[Dict[str, Any]] = field(default_factory=list)  # 涨停列表

    @property
    def create_datetime(self) -> datetime:
        """将时间戳转换为datetime对象"""
        return datetime.fromtimestamp(self.create_time)

    @property
    def update_datetime(self) -> Optional[datetime]:
        """将更新时间戳转换为datetime对象"""
        if self.update_time and int(self.update_time) > 0:
            return datetime.fromtimestamp(self.update_time)
        return None

    @property
    def date(self) -> str:
        """获取创建日期字符串（YYYY-MM-DD）"""
        return self.create_datetime.strftime('%Y-%m-%d')
    
    @property
    def core_stocks(self) -> List[StockInfo]:
        """获取核心股票列表"""
        return [stock for stock in self.stocks if stock.is_zz == "1"]
    
    @property
    def hot_stocks(self) -> List[StockInfo]:
        """获取热门股票列表"""
        return [stock for stock in self.stocks if stock.is_hot == "1"]
    
    @property
    def stock_ids(self) -> List[str]:
        """获取所有相关股票代码"""
        # 从stocks中提取
        stock_ids = [stock.stock_id for stock in self.stocks if stock.stock_id]
        
        # 从tables中提取
        for table in self.tables:
            # 从level1中提取
            for stock in table.level1.stocks:
                if stock.stock_id and stock.stock_id not in stock_ids:
                    stock_ids.append(stock.stock_id)
            
            # 从level2中提取
            for level2_item in table.level2:
                for stock in level2_item.stocks:
                    if stock.stock_id and stock.stock_id not in stock_ids:
                        stock_ids.append(stock.stock_id)
        
        # 从stock_list中提取
        for stock_item in self.stock_list:
            if stock_item.stock_id and stock_item.stock_id not in stock_ids:
                stock_ids.append(stock_item.stock_id)
                
        return stock_ids

    @property
    def all_stocks(self) -> List[StockInfo]:
        """获取所有股票列表，包括表格中的股票"""
        all_stocks = list(self.stocks)  # 创建副本
        
        # 收集表格中的股票
        for table in self.tables:
            # 从level1中收集
            all_stocks.extend(table.level1.stocks)
            
            # 从level2中收集
            for level2_item in table.level2:
                all_stocks.extend(level2_item.stocks)
        
        return all_stocks

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Theme':
        """从字典创建对象"""
        # 解析股票数据
        stocks = [StockInfo.from_dict(item) for item in data.get('Stocks', [])]
        
        # 解析表格数据
        tables = []
        for table_item in data.get('Table', []):
            if isinstance(table_item, dict):
                tables.append(TableItem.from_dict(table_item))
        
        # 解析股票列表
        stock_list = [StockListItem.from_dict(item) for item in data.get('StockList', [])]
        
        return cls(
            id=data.get('ID', ''),
            name=data.get('Name', ''),
            brief_intro=data.get('BriefIntro', ''),
            introduction=data.get('Introduction', ''),
            create_time=int(data.get('CreateTime', 0)),
            update_time=int(data.get('UpdateTime', 0)),
            stocks=stocks,
            class_layer=data.get('ClassLayer', ''),
            desc=data.get('Desc', ''),
            plate_switch=data.get('PlateSwitch', ''),
            stk_switch=data.get('StkSwitch', ''),
            power=int(data.get('Power', 0)),
            tables=tables,
            stock_list=stock_list,
            good_num=int(data.get('GoodNum', 0)),
            com_num=int(data.get('ComNum', 0)),
            is_good=int(data.get('IsGood', 0)),
            zt_list=data.get('ZT', [])
        ) 