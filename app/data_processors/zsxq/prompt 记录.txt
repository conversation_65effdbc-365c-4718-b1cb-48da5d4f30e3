DrissionPage 语法   获取class 的 文本ele('.ng-star-inserted').text，如果是多个类似的class 则用 eles

首先是最外层 class 是 ng-star-inserted， 定位到一个主题

class  showAll
主题中class 为 data 的是时间错
然后是classs  ng-star-inserted   可能就是主题内容，有可能是文本 ，但是有可能是文件或者包含文件。


如果有文件 则
里面可能含有 文件清单    class 是 item ng-star-inserted ，文件可能有多个

item中 有class file-name  就是附件，如  英伟达将恢复H20在华销售，核心利好AIDC链20250716.mp3  或者 新易盛20250716.pdf
通过file的后缀判断文件类型

然后需要对文件进行下载 https://files.zsxq.com/FuuzqpmPcOqChH5JTOy_ayfyxi2c?attname=%E6%96%B0%E6%98%93%E7%9B%9B20250716.pdf&e=1752763510&token=kIxbL07-8jAj8w1n4s9zv64FuZZNEATmlU_Vm6zD:R5esn6Gj8B51kDvCBiSN9mh5638=
这是一个完整的下载链接，请你 仔细分析后实现一个函数来输入文件名 就下载对应的文件，这个函数我要用main 来执行测试下。