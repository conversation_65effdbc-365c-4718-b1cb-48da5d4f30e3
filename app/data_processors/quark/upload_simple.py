import httpx
import os
import time
import hashlib
import random
import string
from tqdm import tqdm


class QuarkUploaderAndSharer:
    def __init__(self, cookies_str: str):
        if not cookies_str:
            raise ValueError("Cookie 不能为空，请手动获取后填入。")
        self.common_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Origin': 'https://pan.quark.cn',
            'Referer': 'https://pan.quark.cn/',
            'Cookie': cookies_str,
        }

    def _generate_request_id(self):
        # 生成一个符合格式的随机 Request ID
        return f"{''.join(random.choices(string.ascii_lowercase + string.digits, k=8))}-{'-'.join([''.join(random.choices(string.ascii_lowercase + string.digits, k=4)) for _ in range(3)])}-{''.join(random.choices(string.ascii_lowercase + string.digits, k=12))}"

    def _calculate_file_hash(self, file_path):
        """计算文件前1MB的MD5和完整的SHA1"""
        md5 = hashlib.md5()
        sha1 = hashlib.sha1()
        with open(file_path, 'rb') as f:
            # MD5 for first 1MB
            md5_chunk = f.read(1024 * 1024)
            md5.update(md5_chunk)
            # SHA1 for the whole file
            f.seek(0)
            while True:
                data = f.read(8192)
                if not data:
                    break
                sha1.update(data)
        return md5.hexdigest(), sha1.hexdigest()

    def upload_file(self, file_path: str, pdir_fid: str = '0') -> str or None:
        """上传文件到指定目录并返回fid (增加了超时和重试机制)"""
        if not os.path.exists(file_path):
            print(f"错误：文件 '{file_path}' 不存在。")
            return None

        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        slice_md5, sha1_val = self._calculate_file_hash(file_path)

        print(f"开始上传文件: {file_name} ({file_size / 1024 / 1024:.2f} MB)")
        print(f"SHA1: {sha1_val}, Slice MD5: {slice_md5}")

        preupload_url = "https://upload.quark.cn/1/clouddrive/file/preupload"
        upload_headers = self.common_headers.copy()
        upload_headers.update({
            "X-Request-ID": self._generate_request_id(),
            "X-Biz-Code": "file_upload",
        })

        preupload_data = {
            "pdir_fid": pdir_fid,
            "name": file_name,
            "size": file_size,
            "slice_size": 4194304,
            "slice_md5": slice_md5,
            "sha1": sha1_val,
            "local_path": file_path.replace("\\", "/"),
            "extra": {"upload_type": 1},
        }

        # ==================== 核心优化点 ====================
        # 1. 定义更长的超时时间 (连接5秒，读取60秒，写入60秒)
        timeout_config = httpx.Timeout(5.0, read=60.0, write=60.0)

        # 2. 定义重试策略：最多重试3次，每次间隔2秒
        max_retries = 3
        # ======================================================

        for attempt in range(max_retries):
            try:
                # 在每次循环中创建一个新的 Client 实例
                with httpx.Client(timeout=timeout_config) as client:
                    # --- 步骤 1: 预上传 ---
                    res = client.post(preupload_url, headers=upload_headers, json=preupload_data)
                    res.raise_for_status()  # 如果状态码不是 2xx，则会引发异常
                    res_json = res.json()

                    if 'data' not in res_json:
                        print(f"预上传失败: {res_json.get('message', '响应中没有 data 字段')}")
                        return None

                    if res_json.get('code') == 31005 or res_json['data'].get('hit_type') == 2:
                        print("文件已存在于云端，实现秒传。")
                        return res_json['data']['fid']

                    upload_id = res_json['data']['upload_id']
                    upload_urls = res_json['data']['upload_urls']
                    print(f"预上传成功, Upload ID: {upload_id}")

                    # --- 步骤 2: 分片上传 ---
                    with open(file_path, 'rb') as f:
                        with tqdm(total=file_size, unit='B', unit_scale=True, desc=f"{file_name} (上传中)") as pbar:
                            for i, url in enumerate(upload_urls):
                                slice_data = f.read(4194304)
                                if not slice_data:
                                    break

                                # 注意：分片上传也需要更长的超时
                                upload_res = client.put(url, content=slice_data, timeout=120.0)
                                upload_res.raise_for_status()
                                pbar.update(len(slice_data))

                    # --- 步骤 3: 完成上传 ---
                    complete_url = "https://upload.quark.cn/1/clouddrive/file/upload/complete"
                    complete_data = {
                        "pdir_fid": pdir_fid, "upload_id": upload_id,
                        "name": file_name, "sha1": sha1_val
                    }
                    complete_res = client.post(complete_url, headers=upload_headers, json=complete_data)
                    complete_res.raise_for_status()
                    complete_json = complete_res.json()

                    if 'data' in complete_json and 'fid' in complete_json['data']:
                        fid = complete_json['data']['fid']
                        print(f"文件上传成功! File ID (fid): {fid}")
                        return fid  # 成功后直接返回，跳出重试循环
                    else:
                        print(f"完成上传失败: {complete_json.get('message', '未知错误')}")
                        return None

            except httpx.TimeoutException as e:
                print(f"请求超时 (尝试 {attempt + 1}/{max_retries})...")
                if attempt + 1 == max_retries:
                    print("已达到最大重试次数，上传失败。")
                    print(f"错误详情: {e}")
                    return None
                time.sleep(2)  # 等待2秒后重试
            except httpx.RequestError as e:
                print(f"请求发生网络错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt + 1 == max_retries:
                    print("已达到最大重试次数，上传失败。")
                    return None
                time.sleep(2)  # 等待2秒后重试
            except Exception as e:
                print(f"发生未知错误: {e}")
                return None  # 对于其他未知错误，直接失败

        return None  # 如果循环结束仍未成功

    # create_share_link 函数无需修改，这里省略以保持简洁
    async def create_share_link(self, fid: str, file_name: str, url_type: int = 1, expired_type: int = 4,
                                password: str = '') -> str or None:
        # ... (代码与之前相同)
        pass

# --- 使用示例 ---
import asyncio


async def main():
    # **************** 请在这里配置 ****************

    # 1. 在这里填入你获取到的 Cookie
    MY_COOKIE = "b-user-id=7f7905fd-4cc6-efc3-f892-70a001730a1c; _UP_A4A_11_=wb9c71a162c04cc29fa1b041d6928c16; _qk_bx_ck_v1=eyJkZXZpY2VJZCI6ImVDeSNBQVBxSEtFdkkwZVR6RnFadFBWendMWlBCT09sSmFGNjhNbXZpTHh5b1VJT09rVlNvSGNmY05GMzN0emNUQkZzMllRPSIsImRldmljZUZpbmdlcnByaW50IjoiNDA2YzE0MDRkMGJkY2FiYjk3N2ZhNTZmOTk3ZGYxZGMifQ==; isg=BLS0_vI2YLMdQPvDdPV11Ue8hXQmjdh3xWDap04WAD8JuVkDdpwRB0JwPfFhbxDP; _qk_bx_um_v1=eyJ1ZCI6IlQyZ0EwWm1ER2hDYmVBNlk0S2k0U3ducnQ5elVRdmpva0hwN1J5Sm50UlNvZEJOWnlDdDh0UGIyeGtOYUVOOVRRdGs9IiwiZXAiOjE3NTMxMDc2NTU0OTN9; _ON_EXT_DVIDN=eCy#AAPqHKEvI0eTzFqZtPVzwLZPBOOlJaF68MmviLxyoUIOOkVSoHcfcNF33tzcTBFs2YQ=; _UP_D_=pc; tfstk=gm7-MOfAYShJ8K7Ji_rmtSpmryN0mofyZT5s-pvoAtBAKTKBZQqyAJpALHv5J6upO66UapVzm_5y8ewgIwUGa_ufbL1lJ31jhQfXNBg7mET93fj_IP4GNuOy-swG-bf7ALOBRQtBAKNv_BLIA26BhnO6sYTCd9tXhBdnNviSVmOX3BTBN9TQMs929e9I1Jy9LVviJgBpfD-SG_uIRZdJcofJhIA4tIt-D_pSRz7vwy91NK3IRdLiSe5CdJ3H5GWAJh6gJq8l6iTXwiFIcLtOVTAVpP3WFZC5PLb7n4v5zssG-tNIRpIffhS1N-mvMi1lknb_B2KPcTSwvw207UjGsw-1PzHwnhRdBUsYh4B14JQGW3ToIddnVSFxYD-WgNVMlHVogW1vMdVD-DoeDydvISErYD-WgIpgiyiEYnUd.; _UP_F7E_8D_=NHgG5W%2BoBaZuDP5AWxS8APjdGzlxqy3HGUOU%2BWEa1%2FC7DUQAEcxlzlqAZLDU1QA2t%2BU6AcUbjiY5AxTG%2B565LlSw6Z0rVeW6PJPcumYzJrr0H0aCp5XmcwfrNL8jXqjlbqkhY5G5J7bqQvbRVfCXla6w9czMCRY7SkvHzt55uX%2FmMBSTZa3pd5uJLjMTpHOqyNbFlpLFVX%2BkkHxAaGgrF%2BoLnBXHj9KcFWCXluONm%2BBnHwsRPxEKCfjaRm4uQsa7hWjiXiMbDqY9q4Aqw0SeP9xHDNuKVjWcWGqEItn%2FZyXUMf5vdVUf70IOu4iKUJgtt%2BWprBYvQIcroc2Mt38X0Pvs4LRfb%2F6ZZa63Bd1rVGJfJITDygNS8cBK%2BGUuli7FiANGEeAipgfDitr5sepdnf%2BYY3F%2Fjv69oAqYfpRLOXis%2BxTXtsN6crie6ed%2FM%2BBM2kPXVA3ndZjsNGuAsgzm9q7amum4haHWW%2FqRKJrlXaA%3D; __pus=ef449f57a33f176b7aa3fe7de9e2ec70AASe7lLqAI+a8ufxeoGzj5zw5CBwrUO/hlQcy6YdZTsKISN+N08D4rIVGshEEiIaoj0cNvqCrThCFfQ7NSk9V+rP; __kp=e2694de0-660f-11f0-af64-e548be92c54a; __kps=AAT+aEB0gFh8mg+n8NtUV6o/; __ktd=BDAvhNyprAIy4YYH4rjl8A==; __uid=AAT+aEB0gFh8mg+n8NtUV6o/; __puus=7b15db75d87316be50772f98c3b2dddbAARnEsb8nz24jwULH1QfGbY9cXYDNotIc8JKKRs4+Ct4vroNkbeYeg3gXv682mkVxaiuJ9wsf6GKlWqFDeY1gxPpVP/Sx34IE1Gy2Zh6B6J85+wdhJnWLlUDjIZhSx0HGxkxqe+ybnWHP0J51JO51Z2PIGYWm4ojJ/TEtFWN8dypoqOLv0zDutYBEgbjSOO7DsDLitvB2e5VyvgqZZTgZGA5"

    # 2. 你要上传的本地文件的完整路径
    FILE_TO_UPLOAD = r"/Users/<USER>/Downloads/WSJ：贝森特如何向特朗普陈述反对解雇美联储主席鲍威尔的理由.pdf"

    # 3. 上传到夸克网盘的哪个文件夹？'0' 代表根目录。
    #    你可以在夸克网页版的网址中找到文件夹的ID，例如:
    #    https://pan.quark.cn/list?fid=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    #    这个 fid 就是文件夹ID。
    TARGET_DIRECTORY_FID = '0'

    # *********************************************

    if MY_COOKIE == "Your Cookie String Here":
        print("请先在代码中填入您的 Cookie！")
        return

    uploader = QuarkUploaderAndSharer(cookies_str=MY_COOKIE)

    # 第一步：上传文件
    uploaded_file_fid = uploader.upload_file(
        file_path=FILE_TO_UPLOAD,
        pdir_fid=TARGET_DIRECTORY_FID
    )

    # 第二步：如果上传成功，则创建分享链接
    if uploaded_file_fid:
        file_name = os.path.basename(FILE_TO_UPLOAD)
        share_link = await uploader.create_share_link(
            fid=uploaded_file_fid,
            file_name=file_name,
            url_type=1,  # 1=公开, 2=加密
            expired_type=4  # 1=1天, 2=7天, 3=30天, 4=永久
        )
        if share_link:
            print("\n----------------------------------------")
            print(f"文件 '{file_name}' 的分享链接是:")
            print(share_link)
            print("----------------------------------------")


if __name__ == '__main__':
    asyncio.run(main())