from jobs.get_all_funds import get_all_funds
from jobs.filter_etfs import filter_etf_funds
from jobs.get_etf_holdings import get_etf_holdings
from jobs.get_etf_industry import get_etf_industry_allocation

def main():
    """
    主函数，按顺序执行所有ETF数据处理任务
    """
    # print("开始执行ETF数据处理流程...")
    #
    # # 步骤1: 获取所有基金列表
    # print("\n--- 步骤 1: 获取所有基金列表 ---")
    # get_all_funds()
    #
    # # 步骤2: 过滤出ETF基金
    print("\n--- 步骤 2: 过滤出ETF基金 ---")
    # filter_etf_funds()
    #
    # # 步骤3: 获取ETF的成分股
    # print("\n--- 步骤 3: 获取ETF成分股 ---")
    get_etf_holdings()
    #
    # # 步骤4: 获取ETF的行业配置
    # print("\n--- 步骤 4: 获取ETF行业配置 ---")
    # get_etf_industry_allocation()
    #
    # print("\n所有ETF数据处理任务执行完毕！")

if __name__ == "__main__":
    main() 