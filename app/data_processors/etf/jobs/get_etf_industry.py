import akshare as ak
import pandas as pd
import os
from tqdm import tqdm
from datetime import datetime

def get_etf_industry_allocation():
    """
    获取所有ETF基金的行业配置
    """
    input_path = "app/data/etf_data/etf_list.csv"
    output_dir = "app/data/etf_data/industry"

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    if not os.path.exists(input_path):
        print(f"错误: 文件 {input_path} 不存在。请先运行 filter_etfs.py")
        return

    etf_list_df = pd.read_csv(input_path)
    symbols = etf_list_df['基金代码'].astype(str)

    print(f"共找到 {len(symbols)} 只ETF基金，开始获取行业配置...")

    current_year = str(datetime.now().year)

    for symbol in tqdm(symbols, desc="获取ETF行业配置"):
        try:
            industry_df = ak.fund_portfolio_industry_allocation_em(symbol=symbol, date=current_year)

            if industry_df.empty:
                print(f"基金代码 {symbol} 在 {current_year} 年没有找到行业配置数据。")
                continue
            
            # 获取截止日期用于文件名
            end_date = industry_df['截止时间'].iloc[0].strftime('%Y-%m-%d')
            output_filename = f"{symbol}_{end_date}.csv"
            output_path = os.path.join(output_dir, output_filename)

            if os.path.exists(output_path):
                # print(f"文件 {output_filename} 已存在，跳过。")
                continue

            industry_df.to_csv(output_path, index=False)
            # print(f"已保存 {symbol} 的行业配置至 {output_path}")

        except Exception as e:
            print(f"获取基金代码 {symbol} 的行业配置时发生错误: {e}")
            continue
            
    print("所有ETF基金的行业配置获取完成。")

if __name__ == "__main__":
    get_etf_industry_allocation() 