import pandas as pd
import os

def filter_etf_funds():
    """
    从所有基金列表中过滤出ETF基金
    """
    input_path = "app/data/etf_data/all_funds.csv"
    output_dir = "app/data/etf_data"
    output_path = os.path.join(output_dir, "etf_list.csv")

    if not os.path.exists(input_path):
        print(f"错误: 文件 {input_path} 不存在。请先运行 get_all_funds.py")
        return

    print("正在读取所有基金列表...")
    fund_list_df = pd.read_csv(input_path)

    print("正在过滤ETF基金...")
    etf_list_df = fund_list_df[
        fund_list_df['基金简称'].str.contains("ETF") &
        ~fund_list_df['基金简称'].str.contains("联接")
    ]

    # 保存为 CSV 文件
    etf_list_df.to_csv(output_path, index=False)
    print(f"ETF基金列表已保存至 {output_path}")

if __name__ == "__main__":
    filter_etf_funds() 