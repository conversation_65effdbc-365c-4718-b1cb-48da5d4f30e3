# 集思录数据采集与管理

本目录包含用于从集思录网站采集、解析和管理金融数据的脚本和相关文件。这些脚本主要用于获取股票股息率数据和行业分类数据，并将其存储到DuckDB数据库中。

## 目录结构

- `get_jisilu_ind_data.py`: 从集思录采集股票股息率数据
- `parse_and_load_industries.py`: 解析行业分类数据并导入到数据库
- `select_industry.md`: 包含行业分类的原始数据（Markdown格式）
- `sql.ddl`: 数据库表结构定义

## 数据表结构

### stock_dividends 表

存储股票股息率及相关财务指标数据。

| 字段名 | 类型 | 描述 |
|-------|------|------|
| stock_id | VARCHAR | 股票代码 |
| stock_nm | VARCHAR | 股票名称 |
| price | VARCHAR | 现价 |
| increase_rt | VARCHAR | 涨跌幅 |
| pe | VARCHAR | PE_TTM |
| pb | VARCHAR | PB |
| dividend_rate | VARCHAR | 股息率 |
| dividend_rate2 | VARCHAR | TTM股息率 |
| dividend_rate5 | VARCHAR | 五年平均股息率 |
| aft_dividend | VARCHAR | 税后股息率 |
| volume | VARCHAR | 成交额(万元) |
| total_value | VARCHAR | 总市值(亿元) |
| float_value | VARCHAR | 流通市值(亿元) |
| roe | VARCHAR | ROE |
| eps_growth_ttm | VARCHAR | EPS增长_TTM |
| ipo_date | VARCHAR | 上市日期 |
| province | VARCHAR | 所在省份 |
| industry_nm | VARCHAR | 所属行业 |
| industry_nm2 | VARCHAR | 申万行业 |
| last_dt | VARCHAR | 最后更新日期 |
| last_time | VARCHAR | 最后更新时间 |
| margin_flg | VARCHAR | 融资融券标识 |
| dividend_rate_base | VARCHAR | 股息率基准 |
| adj_rt | VARCHAR | 复权因子 |
| pb_flag | VARCHAR | PB标识 |
| shares | VARCHAR | 总股本(亿股) |
| eps_growth | VARCHAR | EPS增长率 |
| stdevry | VARCHAR | 标准差 |
| sw_cd | VARCHAR | 申万行业代码 |
| accu_dividend | VARCHAR | 累计分红 |
| price_5year | VARCHAR | 五年最低价 |
| revenue_average | VARCHAR | 营收平均增长率 |
| profit_average | VARCHAR | 利润平均增长率 |
| roe_average | VARCHAR | 平均ROE |
| pb_temperature | VARCHAR | PB温度 |
| pe_temperature | VARCHAR | PE温度 |
| int_debt_rate | VARCHAR | 有息负债率 |
| debt_rate | VARCHAR | 资产负债率 |
| pledge_rt | VARCHAR | 质押比例 |
| cashflow_average | VARCHAR | 现金流平均 |
| dividend_rate_average | VARCHAR | 平均股息率 |
| audit_info | VARCHAR | 审计意见 |
| owned | VARCHAR | 是否持有 |
| holded | VARCHAR | 是否关注 |
| active_flg | VARCHAR | 活跃标志 |

### jisilu_industries 表

存储行业分类层级数据。

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | INTEGER | 唯一行ID |
| depth | INTEGER | 行业层级 (1, 2, 或 3) |
| industry_name | VARCHAR | 当前层级的行业名称 |
| composite_id | VARCHAR | 原始的复合ID, 例如 biho-110102 |
| numeric_id | BIGINT | 从复合ID中提取的数字ID, 例如 110102 |
| level1_name | VARCHAR | 所属一级行业的名称 |
| level1_numeric_id | BIGINT | 所属一级行业的数字ID |
| level2_name | VARCHAR | 所属二级行业的名称 (如果适用) |
| level2_numeric_id | BIGINT | 所属二级行业的数字ID (如果适用) |
| full_path | VARCHAR | 完整的层级路径文本 |

## 脚本说明

### get_jisilu_ind_data.py

从集思录网站采集股票股息率数据，并存储到DuckDB数据库中。

**主要功能**:
- 通过API获取集思录股票股息率数据
- 支持按行业ID遍历获取数据
- 数据清洗和格式化
- 存储到DuckDB数据库的`stock_dividends`表中

**使用方法**:
```bash
python get_jisilu_ind_data.py
```

**注意事项**:
- 需要有效的集思录Cookie才能访问数据
- 默认会遍历所有三级行业获取数据
- 为避免请求过于频繁，脚本会在请求间随机休眠

### parse_and_load_industries.py

解析行业分类数据并导入到DuckDB数据库中。

**主要功能**:
- 从Markdown文件解析行业分类层级数据
- 构建行业层级关系
- 导入数据到DuckDB数据库的`jisilu_industries`表中

**使用方法**:
```bash
python parse_and_load_industries.py
```

**注意事项**:
- 默认从`select_industry.md`文件读取行业数据
- 会自动创建行业表结构

## 数据查询示例

### 获取高股息率股票

```sql
SELECT 
    stock_id, stock_nm, price, dividend_rate, pe, pb, industry_nm
FROM 
    stock_dividends
WHERE 
    CAST(dividend_rate AS FLOAT) > 5.0
    AND CAST(pe AS FLOAT) < 15.0
ORDER BY 
    CAST(dividend_rate AS FLOAT) DESC
LIMIT 10;
```

### 按行业统计股票数量

```sql
SELECT 
    industry_nm, COUNT(*) as stock_count
FROM 
    stock_dividends
GROUP BY 
    industry_nm
ORDER BY 
    stock_count DESC
LIMIT 10;
```

### 获取特定行业的股票

```sql
SELECT 
    s.stock_id, s.stock_nm, s.price, s.dividend_rate, s.pe
FROM 
    stock_dividends s
JOIN 
    jisilu_industries i ON s.industry_nm = i.industry_name
WHERE 
    i.level1_name = '金融'
ORDER BY 
    CAST(s.dividend_rate AS FLOAT) DESC
LIMIT 10;
```

## 依赖项

- Python 3.6+
- pandas
- requests
- duckdb
- 项目自定义的db_manager模块

## 数据更新频率

建议每日或每周运行一次数据采集脚本，以获取最新的股票数据。行业分类数据变动较少，可以按需更新。
