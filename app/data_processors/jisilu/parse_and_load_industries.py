#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
从demo.md文件解析行业分类数据并导入到DuckDB数据库
"""

import os
import sys
import re
import logging
import pandas as pd
from pathlib import Path

# 添加项目根目录到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入db_manager
from app.utils.db_manager import get_db_manager

# 配置日志
from app.utils.logger import get_logger

# 获取当前模块的logger
logger = get_logger(__name__)



def parse_industry_data(md_file_path):
    """
    解析行业分类数据的Markdown文件
    
    Args:
        md_file_path: Markdown文件路径
    
    Returns:
        list: 行业数据列表，每个元素是一个字典
    """
    if not os.path.exists(md_file_path):
        raise FileNotFoundError(f"文件不存在: {md_file_path}")
    
    # 读取Markdown文件内容
    with open(md_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 解析行业数据
    industries = []
    id_counter = 1
    
    # 正则表达式匹配行业数据行
    pattern = r'(\s*)(\*) (一级|二级|三级): ([^(]+) \(ID: ([^)]+)\)'
    matches = re.finditer(pattern, content)
    
    # 用于跟踪当前的一级和二级行业
    current_level1 = None
    current_level2 = None
    
    for match in matches:
        indent = len(match.group(1))
        level_text = match.group(3)
        industry_name = match.group(4).strip()
        composite_id = match.group(5).strip()
        
        # 从复合ID中提取数字ID (例如 'biho-110102' -> 110102)
        numeric_id_match = re.search(r'-(\d+)$', composite_id)
        numeric_id = int(numeric_id_match.group(1)) if numeric_id_match else None
        
        # 确定深度
        depth = 0
        if level_text == '一级':
            depth = 1
            current_level1 = {
                'name': industry_name,
                'numeric_id': numeric_id
            }
            current_level2 = None
        elif level_text == '二级':
            depth = 2
            current_level2 = {
                'name': industry_name,
                'numeric_id': numeric_id
            }
        elif level_text == '三级':
            depth = 3
        
        # 构建行业数据
        industry_data = {
            'id': id_counter,
            'depth': depth,
            'industry_name': industry_name,
            'composite_id': composite_id,
            'numeric_id': numeric_id,
            'level1_name': current_level1['name'] if current_level1 else None,
            'level1_numeric_id': current_level1['numeric_id'] if current_level1 else None,
            'level2_name': current_level2['name'] if current_level2 else None,
            'level2_numeric_id': current_level2['numeric_id'] if current_level2 else None,
        }
        
        # 构建完整路径
        if depth == 1:
            industry_data['full_path'] = industry_name
        elif depth == 2:
            industry_data['full_path'] = f"{current_level1['name']} > {industry_name}"
        elif depth == 3:
            industry_data['full_path'] = f"{current_level1['name']} > {current_level2['name']} > {industry_name}"
        
        industries.append(industry_data)
        id_counter += 1
    
    return industries


def create_industries_table(conn):
    """
    在DuckDB中创建行业表结构，包含字段注释
    
    Args:
        conn: DuckDB连接
    """
    conn.execute("""
    CREATE TABLE IF NOT EXISTS jisilu_industries (
        id INTEGER PRIMARY KEY , -- '唯一行ID',
        depth INTEGER  ,  -- '行业层级 (1, 2, 或 3)',
        industry_name VARCHAR  ,-- '当前层级的行业名称',
        composite_id VARCHAR  ,  -- '原始的复合ID, 例如 biho-110102',
        numeric_id BIGINT ,   -- '从复合ID中提取的数字ID, 例如 110102',
        level1_name VARCHAR  ,  -- '所属一级行业的名称',
        level1_numeric_id BIGINT  ,  -- '所属一级行业的数字ID',
        level2_name VARCHAR  ,  -- '所属二级行业的名称 (如果适用)',
        level2_numeric_id BIGINT ,   -- '所属二级行业的数字ID (如果适用)',
        full_path VARCHAR  -- '完整的层级路径文本'
    )
    """)


def insert_industries_data(conn, industries):
    """
    将行业数据插入到DuckDB表中，使用pandas DataFrame
    
    Args:
        conn: DuckDB连接
        industries: 行业数据列表
    """
    # 先清空表
    conn.execute("DELETE FROM jisilu_industries")
    
    # 将列表转换为pandas DataFrame
    df = pd.DataFrame(industries)
    
    # 使用DuckDB的from_df方法批量插入数据
    conn.execute("INSERT INTO jisilu_industries SELECT * FROM df")
    logger.info(f"已插入 {len(df)} 条行业数据")


def load_industries_from_markdown(md_file=None):
    """
    从Markdown文件解析行业数据并加载到DuckDB数据库
    
    Args:
        md_file: Markdown文件路径，默认为当前脚本所在目录下的demo.md
    
    Returns:
        bool: 是否成功加载
    """
    # 设置默认路径
    current_dir = Path(__file__).parent
    if md_file is None:
        md_file = current_dir / "demo.md"
    else:
        md_file = current_dir / md_file
    
    try:
        # 解析行业数据
        logger.info(f"正在解析文件: {md_file}")
        industries = parse_industry_data(md_file)
        logger.info(f"解析出 {len(industries)} 条行业数据")
        
        # 获取数据库管理器实例，使用项目数据目录下的jisilu.db
        # db_path = os.path.join(project_root, "data", "jisilu.db")
        db_manager = get_db_manager()
        db_manager.connect()
        
        # 创建表结构
        logger.info("正在创建表结构...")
        create_industries_table(db_manager.conn)
        
        # 插入数据
        logger.info("正在插入行业数据...")
        insert_industries_data(db_manager.conn, industries)
        
        # 验证数据导入
        result = db_manager.conn.execute("SELECT COUNT(*) FROM jisilu_industries").fetchone()
        count = result[0] if result else 0
        logger.info(f"成功导入 {count} 条行业数据")
        
        # 显示一些统计信息
        level_counts = db_manager.conn.execute("""
            SELECT depth, COUNT(*) as count 
            FROM jisilu_industries 
            GROUP BY depth 
            ORDER BY depth
        """).fetchall()
        
        for depth, count in level_counts:
            logger.info(f"第{depth}级行业: {count}个")
        
        logger.info(f"行业数据导入完成，数据库路径: ")
        return True
        
    except Exception as e:
        logger.error(f"错误: {str(e)}")
        return False


if __name__ == "__main__":
    # 直接调用函数，不使用argparse
    load_industries_from_markdown('select_industry.md')