-- database.main.jisilu_industries definition

CREATE TABLE jisilu_industries(id INTEGER PRIMARY KEY, depth INTEGER, industry_name VARCHAR, composite_id VARCHAR, numeric_id BIGINT, level1_name VARCHAR, level1_numeric_id BIGINT, level2_name VARCHAR, level2_numeric_id BIGINT, full_path VARCHAR);


-- database.main.stock_dividends definition

CREATE TABLE stock_dividends(stock_id VARCHAR, stock_nm VARCHAR, price VARCHAR, increase_rt VARCHAR, pe VARCHAR, pb VARCHAR, dividend_rate VARCHAR, dividend_rate2 VARCHAR, dividend_rate5 VARCHAR, aft_dividend VARCHAR, volume VARCHAR, total_value VARCHAR, float_value VARCHAR, roe VARCHAR, eps_growth_ttm VARCHAR, ipo_date VARCHAR, province VARCHAR, industry_nm VARCHAR, industry_nm2 VARCHAR, last_dt VARCHAR, last_time VARCHAR, margin_flg VARCHAR, dividend_rate_base VARCHAR, adj_rt VARCHAR, pb_flag VARCHAR, shares VARCHAR, eps_growth VARCHAR, stdevry VARCHAR, sw_cd VARCHAR, accu_dividend VARCHAR, price_5year VARCHAR, revenue_average VARCHAR, profit_average VARCHAR, roe_average VARCHAR, pb_temperature VARCHAR, pe_temperature VARCHAR, int_debt_rate VARCHAR, debt_rate VARCHAR, pledge_rt VARCHAR, cashflow_average VARCHAR, dividend_rate_average VARCHAR, audit_info VARCHAR, "owned" VARCHAR, holded VARCHAR, active_flg VARCHAR);



 jisilu_industries 表定义（带注释）
CREATE TABLE `jisilu_industries` (
  `id` INT PRIMARY KEY COMMENT '唯一行ID',
  `depth` INT COMMENT '行业层级 (1, 2, 或 3)',
  `industry_name` VARCHAR(255) COMMENT '当前层级的行业名称',
  `composite_id` VARCHAR(50) COMMENT '原始的复合ID, 例如 biho-110102',
  `numeric_id` BIGINT COMMENT '从复合ID中提取的数字ID, 例如 110102',
  `level1_name` VARCHAR(255) COMMENT '所属一级行业的名称',
  `level1_numeric_id` BIGINT COMMENT '所属一级行业的数字ID',
  `level2_name` VARCHAR(255) COMMENT '所属二级行业的名称 (如果适用)',
  `level2_numeric_id` BIGINT COMMENT '所属二级行业的数字ID (如果适用)',
  `full_path` VARCHAR(500) COMMENT '完整的层级路径文本'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='集思录行业分类表';

-- stock_dividends 表定义（带注释）
CREATE TABLE `stock_dividends` (
  `stock_id` VARCHAR(20) COMMENT '代码',
  `stock_nm` VARCHAR(100) COMMENT '名称',
  `price` VARCHAR(20) COMMENT '现价',
  `increase_rt` VARCHAR(20) COMMENT '涨跌幅',
  `pe` VARCHAR(20) COMMENT 'PE_TTM',
  `pb` VARCHAR(20) COMMENT 'PB',
  `dividend_rate` VARCHAR(20) COMMENT '股息率',
  `dividend_rate2` VARCHAR(20) COMMENT 'TTM股息率',
  `dividend_rate5` VARCHAR(20) COMMENT '五年平均股息率',
  `aft_dividend` VARCHAR(20) COMMENT '税后股息率',
  `volume` VARCHAR(20) COMMENT '成交额_万元',
  `total_value` VARCHAR(20) COMMENT '总市值_亿元',
  `float_value` VARCHAR(20) COMMENT '流通市值_亿元',
  `roe` VARCHAR(20) COMMENT 'ROE',
  `eps_growth_ttm` VARCHAR(20) COMMENT 'EPS增长_TTM',
  `ipo_date` VARCHAR(20) COMMENT '上市日期',
  `province` VARCHAR(50) COMMENT '所在省份',
  `industry_nm` VARCHAR(100) COMMENT '所属行业',
  `industry_nm2` VARCHAR(100) COMMENT '申万行业',
  `last_dt` VARCHAR(20) COMMENT '最后更新日期',
  `last_time` VARCHAR(20) COMMENT '最后更新时间',
  `margin_flg` VARCHAR(10) COMMENT '融资融券标识',
  `dividend_rate_base` VARCHAR(20) COMMENT '股息率基准',
  `adj_rt` VARCHAR(20) COMMENT '复权因子',
  `pb_flag` VARCHAR(10) COMMENT 'PB标识',
  `shares` VARCHAR(20) COMMENT '总股本_亿股',
  `eps_growth` VARCHAR(20) COMMENT 'EPS增长率',
  `stdevry` VARCHAR(20) COMMENT '标准差',
  `sw_cd` VARCHAR(20) COMMENT '申万行业代码',
  `accu_dividend` VARCHAR(20) COMMENT '累计分红',
  `price_5year` VARCHAR(20) COMMENT '五年最低价',
  `revenue_average` VARCHAR(20) COMMENT '营收平均增长率',
  `profit_average` VARCHAR(20) COMMENT '利润平均增长率',
  `roe_average` VARCHAR(20) COMMENT '平均ROE',
  `pb_temperature` VARCHAR(20) COMMENT 'PB温度',
  `pe_temperature` VARCHAR(20) COMMENT 'PE温度',
  `int_debt_rate` VARCHAR(20) COMMENT '有息负债率',
  `debt_rate` VARCHAR(20) COMMENT '资产负债率',
  `pledge_rt` VARCHAR(20) COMMENT '质押比例',
  `cashflow_average` VARCHAR(20) COMMENT '现金流平均',
  `dividend_rate_average` VARCHAR(20) COMMENT '平均股息率',
  `audit_info` VARCHAR(100) COMMENT '审计意见',
  `owned` VARCHAR(5) COMMENT '是否持有',
  `holded` VARCHAR(5) COMMENT '是否关注',
  `active_flg` VARCHAR(5) COMMENT '活跃标志',
  KEY `idx_stock_id` (`stock_id`),
  KEY `idx_industry_nm` (`industry_nm`),
  KEY `idx_dividend_rate` (`dividend_rate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票股息率数据表';