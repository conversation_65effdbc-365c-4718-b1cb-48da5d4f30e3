"""
每日复盘任务

负责每日数据更新、指标计算和分析报告生成
"""
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import pandas as pd

from ..data.miniqmt_collector import MiniQMTCollector as DataCollector
from ..data.manager import DataManager
from ..indicators.engine import IndicatorEngine
from ..strategies.engine import StrategyEngine
from ..patterns.engine import PatternEngine
from ..config import settings

logger = logging.getLogger(__name__)

class DailyReviewTask:
    """每日复盘任务"""
    
    def __init__(self):
        self.data_collector = DataCollector()
        self.data_manager = DataManager()
        self.indicator_engine = IndicatorEngine()
        self.strategy_engine = StrategyEngine()
        self.pattern_engine = PatternEngine()
        
        # 报告保存路径
        self.report_dir = Path(settings.data.processed_data_path) / "reports"
        self.report_dir.mkdir(parents=True, exist_ok=True)
    
    def run_daily_review(self, date: str = None) -> Dict[str, Any]:
        """执行每日复盘
        
        Args:
            date: 复盘日期，默认为今天
            
        Returns:
            复盘结果
        """
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
        
        logger.info(f"开始执行每日复盘: {date}")
        
        review_result = {
            'date': date,
            'start_time': datetime.now().isoformat(),
            'steps': {},
            'summary': {},
            'errors': []
        }
        
        try:
            # 步骤1: 更新数据
            logger.info("步骤1: 更新股票数据")
            data_update_result = self._update_stock_data()
            review_result['steps']['data_update'] = data_update_result
            
            # 步骤2: 计算技术指标
            logger.info("步骤2: 计算技术指标")
            indicator_result = self._calculate_indicators()
            review_result['steps']['indicators'] = indicator_result
            
            # 步骤3: 策略分析
            logger.info("步骤3: 策略信号分析")
            strategy_result = self._analyze_strategies()
            review_result['steps']['strategies'] = strategy_result
            
            # 步骤4: 形态识别
            logger.info("步骤4: K线形态识别")
            pattern_result = self._recognize_patterns()
            review_result['steps']['patterns'] = pattern_result
            
            # 步骤5: 生成报告
            logger.info("步骤5: 生成分析报告")
            report_result = self._generate_report(review_result)
            review_result['steps']['report'] = report_result
            
            # 汇总结果
            review_result['summary'] = self._summarize_results(review_result)
            review_result['end_time'] = datetime.now().isoformat()
            review_result['success'] = True
            
            logger.info(f"每日复盘完成: {date}")
            
        except Exception as e:
            error_msg = f"每日复盘失败: {e}"
            logger.error(error_msg)
            review_result['errors'].append(error_msg)
            review_result['success'] = False
            review_result['end_time'] = datetime.now().isoformat()
        
        return review_result
    
    def _update_stock_data(self) -> Dict[str, Any]:
        """更新股票数据（演示模式：只检查现有数据）"""
        try:
            # 获取现有股票列表
            symbols = self.data_manager.get_available_symbols()

            # 检查数据完整性
            valid_count = 0
            for symbol in symbols:
                data = self.data_manager.load_stock_data(symbol)
                if not data.empty:
                    valid_count += 1

            return {
                'success': True,
                'total_stocks': len(symbols),
                'updated_stocks': valid_count,
                'success_rate': valid_count / len(symbols) if symbols else 0,
                'details': f"检查了 {len(symbols)} 只股票，{valid_count} 只有效",
                'note': "演示模式：跳过实际数据更新"
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _calculate_indicators(self) -> Dict[str, Any]:
        """计算技术指标"""
        try:
            symbols = self.data_manager.get_available_symbols()
            
            calculated_count = 0
            error_count = 0
            indicator_summary = {}
            
            for symbol in symbols:
                try:
                    # 加载数据
                    data = self.data_manager.load_stock_data(symbol)
                    
                    if data.empty:
                        continue
                    
                    # 计算指标
                    data_with_indicators = self.indicator_engine.calculate_all_indicators(data)
                    
                    # 获取最新指标值
                    latest = data_with_indicators.iloc[-1]
                    
                    # 保存关键指标
                    indicator_summary[symbol] = {
                        'date': latest['date'].isoformat(),
                        'close': float(latest['close']),
                        'RSI': float(latest['RSI']) if 'RSI' in latest.index and not pd.isna(latest['RSI']) else None,
                        'MACD': float(latest['MACD']) if 'MACD' in latest.index and not pd.isna(latest['MACD']) else None,
                        'KDJ_K': float(latest['KDJ_K']) if 'KDJ_K' in latest.index and not pd.isna(latest['KDJ_K']) else None
                    }
                    
                    calculated_count += 1
                    
                except Exception as e:
                    logger.warning(f"计算股票 {symbol} 指标失败: {e}")
                    error_count += 1
            
            return {
                'success': True,
                'calculated_stocks': calculated_count,
                'error_stocks': error_count,
                'indicator_summary': indicator_summary
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _analyze_strategies(self) -> Dict[str, Any]:
        """分析策略信号"""
        try:
            symbols = self.data_manager.get_available_symbols()
            
            buy_signals = []
            sell_signals = []
            analysis_count = 0
            
            for symbol in symbols:
                try:
                    # 加载数据
                    data = self.data_manager.load_stock_data(symbol)
                    
                    if data.empty or len(data) < 20:
                        continue
                    
                    # 计算指标
                    data_with_indicators = self.indicator_engine.calculate_all_indicators(data)
                    
                    # 分析策略
                    comprehensive_signal = self.strategy_engine.get_comprehensive_signal(data_with_indicators)
                    
                    signal_data = {
                        'symbol': symbol,
                        'signal': comprehensive_signal['signal'],
                        'strength': comprehensive_signal['strength'],
                        'reason': comprehensive_signal['reason'],
                        'close': float(data_with_indicators.iloc[-1]['close'])
                    }
                    
                    if comprehensive_signal['signal'] == 'BUY' and comprehensive_signal['strength'] >= 0.5:
                        buy_signals.append(signal_data)
                    elif comprehensive_signal['signal'] == 'SELL' and comprehensive_signal['strength'] >= 0.5:
                        sell_signals.append(signal_data)
                    
                    analysis_count += 1
                    
                except Exception as e:
                    logger.warning(f"分析股票 {symbol} 策略失败: {e}")
            
            # 按强度排序
            buy_signals.sort(key=lambda x: x['strength'], reverse=True)
            sell_signals.sort(key=lambda x: x['strength'], reverse=True)
            
            return {
                'success': True,
                'analyzed_stocks': analysis_count,
                'buy_signals': buy_signals[:10],  # 只保留前10个
                'sell_signals': sell_signals[:10],
                'buy_count': len(buy_signals),
                'sell_count': len(sell_signals)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _recognize_patterns(self) -> Dict[str, Any]:
        """识别K线形态"""
        try:
            symbols = self.data_manager.get_available_symbols()
            
            all_patterns = {}
            pattern_count = 0
            analysis_count = 0
            
            for symbol in symbols:
                try:
                    # 加载数据
                    data = self.data_manager.load_stock_data(symbol)
                    
                    if data.empty or len(data) < 5:
                        continue
                    
                    # 识别形态
                    patterns = self.pattern_engine.detect_all_patterns(data)
                    
                    for pattern_name, detections in patterns.items():
                        if pattern_name not in all_patterns:
                            all_patterns[pattern_name] = []
                        
                        for detection in detections:
                            detection['symbol'] = symbol
                            all_patterns[pattern_name].append(detection)
                            pattern_count += 1
                    
                    analysis_count += 1
                    
                except Exception as e:
                    logger.warning(f"识别股票 {symbol} 形态失败: {e}")
            
            # 统计最近的形态
            recent_patterns = []
            for pattern_name, detections in all_patterns.items():
                for detection in detections:
                    detection['pattern_name'] = pattern_name
                    recent_patterns.append(detection)
            
            # 按日期排序
            recent_patterns.sort(key=lambda x: x['date'], reverse=True)
            
            return {
                'success': True,
                'analyzed_stocks': analysis_count,
                'total_patterns': pattern_count,
                'pattern_types': len(all_patterns),
                'recent_patterns': recent_patterns[:20]  # 最近20个形态
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_report(self, review_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成分析报告"""
        try:
            date = review_result['date']
            report_file = self.report_dir / f"daily_review_{date}.json"
            
            # 生成报告内容
            report_content = {
                'date': date,
                'generated_at': datetime.now().isoformat(),
                'summary': review_result.get('summary', {}),
                'data_update': review_result['steps'].get('data_update', {}),
                'indicators': review_result['steps'].get('indicators', {}),
                'strategies': review_result['steps'].get('strategies', {}),
                'patterns': review_result['steps'].get('patterns', {}),
                'errors': review_result.get('errors', [])
            }
            
            # 保存报告
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_content, f, ensure_ascii=False, indent=2)
            
            return {
                'success': True,
                'report_file': str(report_file),
                'report_size': report_file.stat().st_size
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _summarize_results(self, review_result: Dict[str, Any]) -> Dict[str, Any]:
        """汇总复盘结果"""
        summary = {
            'date': review_result['date'],
            'overall_success': True,
            'step_results': {}
        }
        
        # 汇总各步骤结果
        for step_name, step_result in review_result['steps'].items():
            summary['step_results'][step_name] = step_result.get('success', False)
            if not step_result.get('success', False):
                summary['overall_success'] = False
        
        # 数据更新汇总
        data_update = review_result['steps'].get('data_update', {})
        if data_update.get('success'):
            summary['data_update'] = {
                'updated_stocks': data_update.get('updated_stocks', 0),
                'success_rate': data_update.get('success_rate', 0)
            }
        
        # 策略信号汇总
        strategies = review_result['steps'].get('strategies', {})
        if strategies.get('success'):
            summary['signals'] = {
                'buy_count': strategies.get('buy_count', 0),
                'sell_count': strategies.get('sell_count', 0),
                'analyzed_stocks': strategies.get('analyzed_stocks', 0)
            }
        
        # 形态识别汇总
        patterns = review_result['steps'].get('patterns', {})
        if patterns.get('success'):
            summary['patterns'] = {
                'total_patterns': patterns.get('total_patterns', 0),
                'pattern_types': patterns.get('pattern_types', 0),
                'analyzed_stocks': patterns.get('analyzed_stocks', 0)
            }
        
        return summary
