"""
批量离线计算指标脚本

预计算所有股票的技术指标并存储到数据库中
"""
import sys
import argparse
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.data.miniqmt_collector import MiniQMTCollector
from app.strategy.data.manager import DataManager
from app.strategy.indicators.engine import IndicatorEngine
from app.strategy.config import settings

class IndicatorCalculationManager:
    """指标计算管理器"""

    def __init__(self):
        self.collector = MiniQMTCollector()
        self.manager = DataManager()
        self.indicator_engine = IndicatorEngine()

    def get_available_stocks(self, limit: Optional[int] = None) -> List[str]:
        """获取可用的股票列表"""
        try:
            # 从数据文件中获取股票列表
            raw_path = Path(settings.data.raw_data_path)
            if not raw_path.exists():
                print("❌ 数据目录不存在")
                return []

            # 获取最新的数据文件
            latest_files = []
            for year_dir in sorted(raw_path.iterdir(), reverse=True):
                if not year_dir.is_dir() or not year_dir.name.isdigit():
                    continue

                for month_dir in sorted(year_dir.iterdir(), reverse=True):
                    if not month_dir.is_dir():
                        continue

                    files = sorted(month_dir.glob("*.parquet"), reverse=True)
                    if files:
                        latest_files.extend(files[:3])  # 取最新3个文件
                        break

                if latest_files:
                    break

            if not latest_files:
                print("❌ 没有找到数据文件")
                return []

            # 从最新文件中提取股票代码
            all_symbols = set()
            for file_path in latest_files:
                try:
                    df = pd.read_parquet(file_path)
                    if not df.empty and 'symbol' in df.columns:
                        all_symbols.update(df['symbol'].unique())
                except Exception as e:
                    print(f"⚠ 读取文件 {file_path} 失败: {e}")

            symbols = sorted(list(all_symbols))

            if limit:
                symbols = symbols[:limit]

            print(f"📊 找到 {len(symbols)} 只股票")
            return symbols

        except Exception as e:
            print(f"❌ 获取股票列表失败: {e}")
            return []

    def calculate_indicators_for_stock(self, symbol: str,
                                     start_date: Optional[str] = None,
                                     end_date: Optional[str] = None) -> Dict:
        """计算单只股票的指标"""
        try:
            # 设置默认日期范围
            if not end_date:
                end_date = datetime.now().strftime("%Y-%m-%d")
            if not start_date:
                start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")

            # 加载股票数据
            data = self.manager.load_stock_data_from_daily_files(symbol, start_date, end_date)

            if data.empty:
                return {
                    'symbol': symbol,
                    'success': False,
                    'error': '无数据',
                    'indicators': {}
                }

            # 计算技术指标
            indicators = {}

            # 基础指标
            try:
                # 移动平均线
                indicators['ma5'] = self.indicator_engine.calculate_ma(data, 5)
                indicators['ma10'] = self.indicator_engine.calculate_ma(data, 10)
                indicators['ma20'] = self.indicator_engine.calculate_ma(data, 20)
                indicators['ma60'] = self.indicator_engine.calculate_ma(data, 60)

                # RSI
                indicators['rsi'] = self.indicator_engine.calculate_rsi(data)

                # MACD
                macd_result = self.indicator_engine.calculate_macd(data)
                indicators.update(macd_result)

                # 布林带
                bollinger_result = self.indicator_engine.calculate_bollinger_bands(data)
                indicators.update(bollinger_result)

                # KDJ
                kdj_result = self.indicator_engine.calculate_kdj(data)
                indicators.update(kdj_result)

            except Exception as e:
                return {
                    'symbol': symbol,
                    'success': False,
                    'error': f'指标计算失败: {e}',
                    'indicators': {}
                }

            return {
                'symbol': symbol,
                'success': True,
                'error': None,
                'indicators': indicators,
                'data_points': len(data)
            }

        except Exception as e:
            return {
                'symbol': symbol,
                'success': False,
                'error': str(e),
                'indicators': {}
            }

    def batch_calculate_indicators(self, symbols: List[str],
                                 batch_size: int = 100,
                                 start_date: Optional[str] = None,
                                 end_date: Optional[str] = None) -> Dict:
        """批量计算指标"""
        print(f"🚀 开始批量计算 {len(symbols)} 只股票的技术指标")

        results = {
            'total': len(symbols),
            'success': 0,
            'failed': 0,
            'details': []
        }

        # 分批处理
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(symbols) + batch_size - 1) // batch_size

            print(f"\n📦 处理第 {batch_num}/{total_batches} 批 ({len(batch_symbols)} 只股票)")

            for j, symbol in enumerate(batch_symbols, 1):
                print(f"  [{j}/{len(batch_symbols)}] 计算股票 {symbol} 的指标...")

                result = self.calculate_indicators_for_stock(symbol, start_date, end_date)
                results['details'].append(result)

                if result['success']:
                    results['success'] += 1
                    print(f"    ✅ 成功: {result['data_points']} 个数据点")
                else:
                    results['failed'] += 1
                    print(f"    ❌ 失败: {result['error']}")

        print(f"\n🎉 批量计算完成!")
        print(f"📊 总计: {results['total']} 只股票")
        print(f"✅ 成功: {results['success']} 只")
        print(f"❌ 失败: {results['failed']} 只")
        print(f"📈 成功率: {results['success']/results['total']*100:.1f}%")

        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量计算股票技术指标')
    parser.add_argument('--symbols', nargs='+', help='指定股票代码列表')
    parser.add_argument('--limit', type=int, help='限制处理的股票数量')
    parser.add_argument('--batch-size', type=int, default=100, help='批次大小 (默认: 100)')
    parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')

    args = parser.parse_args()

    print("🚀 批量技术指标计算脚本")
    print("=" * 50)

    # 创建计算管理器
    manager = IndicatorCalculationManager()

    # 获取股票列表
    if args.symbols:
        symbols = args.symbols
        print(f"📋 使用指定的股票列表: {symbols}")
    else:
        symbols = manager.get_available_stocks(args.limit)
        if not symbols:
            print("❌ 无法获取股票列表")
            return

    # 执行批量计算
    results = manager.batch_calculate_indicators(
        symbols=symbols,
        batch_size=args.batch_size,
        start_date=args.start_date,
        end_date=args.end_date
    )

    # 显示详细结果
    if results['failed'] > 0:
        print(f"\n❌ 失败的股票:")
        for detail in results['details']:
            if not detail['success']:
                print(f"  {detail['symbol']}: {detail['error']}")

if __name__ == "__main__":
    main()
    
    try:
        # 获取股票列表
        if args.symbols:
            symbols = args.symbols
            print(f"📋 指定股票: {len(symbols)} 只")
        else:
            stock_list = data_service.get_stock_list()
            symbols = [stock['symbol'] for stock in stock_list]
            print(f"📋 获取股票列表: {len(symbols)} 只")
        
        # 限制数量
        if args.limit:
            symbols = symbols[:args.limit]
            print(f"📊 限制处理: {len(symbols)} 只")
        
        # 开始批量计算
        print(f"⚙️ 配置: 并发数={args.workers}, 批次大小={args.batch_size}")
        
        if args.force:
            print("🔄 强制重新计算模式")
        
        confirm = input(f"\n确认开始批量计算？(y/n): ").lower().strip()
        if confirm != 'y':
            print("已取消")
            return
        
        # 创建计算器实例
        calculator = OfflineIndicatorCalculator()

        # 执行批量计算
        result = calculator.batch_calculate_indicators(
            symbols=symbols,
            max_workers=args.workers,
            batch_size=args.batch_size
        )
        
        # 显示结果
        print("\n" + "=" * 50)
        print("🎉 批量计算完成!")
        print(f"📊 总计: {result['total']} 只股票")
        print(f"✅ 成功: {result['success']} 只")
        print(f"❌ 失败: {result['failed']} 只")
        print(f"⏱ 耗时: {result['elapsed_time']:.1f} 秒")
        print(f"📈 成功率: {result['success_rate']*100:.1f}%")
        
        if result['success'] > 0:
            print("\n💡 现在可以启动Web服务使用预计算的指标:")
            print("python -m uvicorn app.main:app --reload")
        
    except KeyboardInterrupt:
        print("\n⚠ 用户中断")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭数据库连接
        try:
            if 'calculator' in locals():
                calculator.close()
        except:
            pass

if __name__ == "__main__":
    main()
