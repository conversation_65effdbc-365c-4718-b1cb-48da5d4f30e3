# 量化分析系统

一个专业的股票量化分析系统，提供数据采集、技术指标计算、策略分析、K线形态识别等完整功能。

## 🚀 系统特性

### 核心功能
- **数据采集与存储**: 基于akshare获取东方财富历史数据，按年月分区存储为parquet格式
- **技术指标计算**: 实现32种核心技术指标，包括MACD、KDJ、BOLL、RSI等
- **策略信号分析**: 多种买卖信号判断，支持KDJ、RSI、CCI、CR、WR、VR等策略
- **K线形态识别**: 自动识别32种经典K线形态，如吞噬形态、锤头线、十字星等
- **定时任务调度**: 每日复盘计算系统，自动更新数据和生成分析报告
- **RESTful API**: 完整的API接口，支持数据查询、指标计算、策略筛选等
- **高性能存储**: 基于DuckDB和Parquet的高效数据存储方案

### 技术架构
```
数据层 -> 计算层 -> 分析层 -> 接口层
  |        |        |        |
数据采集   技术指标   策略分析   API服务
数据存储   形态识别   复盘报告   定时任务
```

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

主要依赖：
- akshare: 数据采集
- pandas: 数据处理
- talib: 技术指标计算
- duckdb: 高性能数据库
- pyarrow: Parquet文件支持
- flask: API服务

## 🎯 快速开始

### 1. 数据采集
```python
# 运行数据采集脚本
python app/strategy/scripts/data_collection.py
```

### 2. 技术指标计算
```python
# 运行技术指标计算脚本
python app/strategy/scripts/batch_calculate_indicators.py
```

## 🧪 测试

### 运行单元测试
```bash
# 数据采集测试
python app/strategy/tests/test_data_collector.py

# 技术指标测试
python app/strategy/tests/test_indicators.py

# 集成测试
python app/strategy/tests/test_integration.py
```

## 🔧 配置

系统配置文件位于 `app/strategy/config.py`，主要配置项：

```python
# 数据存储路径
data_root = "data/strategy"
raw_data_path = "data/strategy/raw"
processed_data_path = "data/strategy/processed"

# 数据库配置
database_path = "data/strategy/strategy.duckdb"

# 技术指标参数
macd_params = {"fast": 12, "slow": 26, "signal": 9}
kdj_params = {"k_period": 9, "d_period": 3, "j_period": 3}
```

## 📊 API接口
[
### 启动API服务
```python
python app/strategy/examples/api_server.py
```

### 主要端点
- `GET /api/strategy/health` - 健康检查
- `GET /api/strategy/stocks` - 获取股票列表
- `GET /api/strategy/stocks/{symbol}/data` - 获取股票数据
- `GET /api/strategy/stocks/{symbol}/indicators` - 获取技术指标
- `GET /api/strategy/stocks/{symbol}/signals` - 获取策略信号
- `GET /api/strategy/stocks/{symbol}/patterns` - 获取K线形态
- `POST /api/strategy/screening` - 股票筛选
- `GET /api/strategy/market/overview` - 市场概览
- `GET /api/strategy/reports/daily` - 每日复盘报告

## 📈 技术指标

### 支持的指标（32种）
1. **趋势指标**: MACD, BOLL, TRIX, SMA, EMA, DMA, AMA
2. **震荡指标**: RSI, KDJ, CCI, WR, ROC, PSY, BIAS
3. **成交量指标**: OBV, VR, MAVR, MFI, VWMA
4. **动量指标**: DMI, +DI, -DI, DX, ADX, ADXR
5. **波动指标**: TR, ATR, SAR, BRAR, EMV
6. **其他指标**: CR, TEMA, PPO, WT, Supertrend, DPO, VHF, RVI, FI, ENE, STOCHRSI

## 🔍 K线形态识别

### 支持的形态（32种）
**第一梯队（核心形态）**:
- 吞噬形态 (Engulfing Pattern)
- 锤头线 (Hammer)
- 射击之星 (Shooting Star)
- 十字星 (Doji)
- 蜻蜓十字 (Dragonfly Doji)
- 墓碑十字 (Gravestone Doji)
- 晨星 (Morning Star)
- 暮星 (Evening Star)
- 三个白兵 (Three White Soldiers)
- 三只乌鸦 (Three Black Crows)
- 乌云压顶 (Dark Cloud Cover)
- 刺透形态 (Piercing Pattern)

**第二梯队（重要形态）**:
- 母子线 (Harami)
- 十字孕线 (Harami Cross)
- 上吊线 (Hanging Man)
- 倒锤头 (Inverted Hammer)
- 等等...

## ⏰ 定时任务]()

### 设置每日复盘
```python
from app.strategy.tasks.scheduler import TaskScheduler
from app.strategy.tasks.daily_review import DailyReviewTask

scheduler = TaskScheduler()
review_task = DailyReviewTask()

# 添加每日复盘任务
scheduler.add_task(
    task_id="daily_review",
    name="每日复盘任务",
    func=review_task.run_daily_review,
    schedule_time="20:00",
    description="每日复盘分析"
)

scheduler.start()
```

## 📁 目录结构

```
app/strategy/
├── __init__.py          # 模块初始化
├── config.py            # 系统配置
├── README.md            # 说明文档
├── data/                # 数据层
│   ├── collector.py     # 数据采集器
│   ├── manager.py       # 数据管理器
│   ├── validator.py     # 数据验证器
│   └── transformer.py   # 数据转换器
├── indicators/          # 指标层
│   └── engine.py        # 技术指标引擎
├── strategies/          # 策略层
│   └── engine.py        # 策略引擎
├── patterns/            # 形态层
│   └── engine.py        # 形态识别引擎
├── analysis/            # 分析层
│   └── engine.py        # 分析引擎
├── tasks/               # 任务层
│   ├── scheduler.py     # 任务调度器
│   └── daily_review.py  # 每日复盘任务
├── api/                 # 接口层
│   ├── models.py        # 数据模型
│   └── routes.py        # API路由
├── utils/               # 工具层
├── tests/               # 测试
│   ├── test_data_collector.py
│   ├── test_indicators.py
│   └── test_integration.py
└── examples/            # 示例
    ├── collect_sample_data.py
    ├── calculate_indicators.py
    ├── stock_screening.py
    ├── pattern_recognition.py
    ├── scheduled_tasks.py
    ├── api_server.py
    └── complete_demo.py
```

## 🎯 使用场景

1. **个人投资**: 技术分析、选股筛选
2. **量化研究**: 策略回测、因子分析
3. **机构应用**: 风险管理、投资决策
4. **教育培训**: 量化投资教学

## 🔮 扩展开发

### 添加新的技术指标
```python
def _calculate_custom_indicator(self, data: pd.DataFrame) -> pd.Series:
    """自定义指标计算"""
    # 实现指标计算逻辑
    pass
```

### 添加新的策略
```python
def _custom_strategy(self, data: pd.DataFrame) -> Dict[str, Any]:
    """自定义策略"""
    # 实现策略逻辑
    return {
        'signal': 'BUY',
        'strength': 0.8,
        'reason': '自定义策略信号'
    }
```

### 添加新的K线形态
```python
def _detect_custom_pattern(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
    """自定义形态识别"""
    # 实现形态识别逻辑
    pass
```

## 📝 注意事项

1. **数据质量**: 确保网络连接稳定，akshare数据源可用
2. **计算性能**: 大量数据计算时注意内存使用
3. **策略风险**: 量化信号仅供参考，投资需谨慎
4. **系统监控**: 生产环境建议配置监控和日志

## 🤝 贡献

欢迎提交Issue和Pull Request来改进系统。

## 📄 许可证

本项目采用MIT许可证。

---

**免责声明**: 本系统仅用于技术研究和教育目的，不构成投资建议。投资有风险，入市需谨慎。
