"""
批量高性能指标计算器

提供多进程并行计算和增量更新功能
"""
import os
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import pandas as pd
import numpy as np
from multiprocessing import cpu_count

from app.strategy.indicators.engine import IndicatorEngine
from app.strategy.indicators.parquet_storage import ParquetIndicatorStorage
from app.strategy.services.data_service import DataService
from app.strategy.config import settings

logger = logging.getLogger(__name__)


class BatchIndicatorCalculator:
    """批量指标计算器"""
    
    def __init__(self, max_workers: int = None):
        self.max_workers = max_workers or min(cpu_count(), 8)
        self.storage = ParquetIndicatorStorage()
        self.data_service = DataService()
        
        # 性能配置
        self.batch_size = 100  # 每批处理的股票数量
        self.chunk_size = 50   # 每个进程处理的股票数量
        
        logger.info(f"初始化批量指标计算器，最大工作进程数: {self.max_workers}")
    
    def calculate_indicators_for_date(self, date: str, symbols: List[str] = None, 
                                    force_recalculate: bool = False) -> Dict[str, Any]:
        """计算指定日期的指标
        
        Args:
            date: 日期字符串 (YYYY-MM-DD)
            symbols: 股票代码列表，如果为None则处理所有股票
            force_recalculate: 是否强制重新计算
            
        Returns:
            计算结果统计
        """
        start_time = time.time()
        logger.info(f"开始计算日期 {date} 的指标")
        
        try:
            # 检查是否已经计算过
            if not force_recalculate:
                existing_data = self.storage.load_indicators_by_date(date)
                if not existing_data.empty:
                    logger.info(f"日期 {date} 的指标已存在，跳过计算")
                    return {
                        'success': True,
                        'date': date,
                        'total_symbols': len(existing_data),
                        'calculated': 0,
                        'skipped': len(existing_data),
                        'failed': 0,
                        'elapsed_time': time.time() - start_time
                    }
            
            # 获取当日股票数据
            daily_data = self.data_service.load_daily_data(date)
            if daily_data.empty:
                logger.warning(f"日期 {date} 无股票数据")
                return {
                    'success': False,
                    'error': f'日期 {date} 无股票数据',
                    'elapsed_time': time.time() - start_time
                }
            
            # 过滤股票
            if symbols:
                daily_data = daily_data[daily_data['symbol'].isin(symbols)]
            
            available_symbols = daily_data['symbol'].unique().tolist()
            logger.info(f"日期 {date} 可用股票数量: {len(available_symbols)}")
            
            # 分批并行计算
            all_indicators = []
            calculated_count = 0
            failed_count = 0
            
            # 将股票分组
            symbol_chunks = [available_symbols[i:i + self.chunk_size] 
                           for i in range(0, len(available_symbols), self.chunk_size)]
            
            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交任务
                future_to_chunk = {}
                for chunk in symbol_chunks:
                    chunk_data = daily_data[daily_data['symbol'].isin(chunk)]
                    future = executor.submit(
                        _calculate_indicators_for_symbols_chunk,
                        chunk_data, date
                    )
                    future_to_chunk[future] = chunk
                
                # 收集结果
                for future in as_completed(future_to_chunk):
                    chunk = future_to_chunk[future]
                    try:
                        chunk_result = future.result()
                        if chunk_result['success']:
                            all_indicators.append(chunk_result['data'])
                            calculated_count += len(chunk_result['data'])
                            logger.debug(f"完成计算股票块: {len(chunk)} 只股票")
                        else:
                            failed_count += len(chunk)
                            logger.error(f"计算股票块失败: {chunk_result.get('error', '未知错误')}")
                    except Exception as e:
                        failed_count += len(chunk)
                        logger.error(f"处理股票块异常: {e}")
            
            # 合并所有指标数据
            if all_indicators:
                combined_indicators = pd.concat(all_indicators, ignore_index=True)
                
                # 保存指标数据
                save_success = self.storage.save_indicators_by_date(date, combined_indicators)
                if not save_success:
                    logger.error(f"保存日期 {date} 指标数据失败")
                    return {
                        'success': False,
                        'error': '保存指标数据失败',
                        'elapsed_time': time.time() - start_time
                    }
            
            elapsed_time = time.time() - start_time
            logger.info(f"日期 {date} 指标计算完成: 成功 {calculated_count}, 失败 {failed_count}, 耗时 {elapsed_time:.2f}s")
            
            return {
                'success': True,
                'date': date,
                'total_symbols': len(available_symbols),
                'calculated': calculated_count,
                'skipped': 0,
                'failed': failed_count,
                'elapsed_time': elapsed_time
            }
            
        except Exception as e:
            logger.error(f"计算日期 {date} 指标失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'elapsed_time': time.time() - start_time
            }
    
    def calculate_indicators_incremental(self, start_date: str = None, 
                                       end_date: str = None,
                                       symbols: List[str] = None) -> Dict[str, Any]:
        """增量计算指标
        
        Args:
            start_date: 开始日期，如果为None则从最新日期开始
            end_date: 结束日期，如果为None则到今天
            symbols: 股票代码列表
            
        Returns:
            计算结果统计
        """
        start_time = time.time()
        logger.info("开始增量计算指标")
        
        try:
            # 确定日期范围
            if start_date is None:
                latest_date = self.storage.get_latest_date()
                if latest_date:
                    # 从最新日期的下一天开始
                    start_date_obj = datetime.strptime(latest_date, '%Y-%m-%d') + timedelta(days=1)
                    start_date = start_date_obj.strftime('%Y-%m-%d')
                else:
                    # 如果没有历史数据，从30天前开始
                    start_date_obj = datetime.now() - timedelta(days=30)
                    start_date = start_date_obj.strftime('%Y-%m-%d')
            
            if end_date is None:
                end_date = datetime.now().strftime('%Y-%m-%d')
            
            logger.info(f"增量计算日期范围: {start_date} 到 {end_date}")
            
            # 获取需要计算的日期列表
            dates_to_calculate = self._get_trading_dates(start_date, end_date)
            if not dates_to_calculate:
                logger.info("没有需要计算的日期")
                return {
                    'success': True,
                    'total_dates': 0,
                    'calculated_dates': 0,
                    'skipped_dates': 0,
                    'failed_dates': 0,
                    'elapsed_time': time.time() - start_time
                }
            
            logger.info(f"需要计算的日期数量: {len(dates_to_calculate)}")
            
            # 逐日计算
            results = []
            calculated_dates = 0
            failed_dates = 0
            
            for date in dates_to_calculate:
                logger.info(f"计算日期: {date}")
                result = self.calculate_indicators_for_date(date, symbols)
                results.append(result)
                
                if result['success']:
                    calculated_dates += 1
                else:
                    failed_dates += 1
                    logger.error(f"日期 {date} 计算失败: {result.get('error', '未知错误')}")
            
            elapsed_time = time.time() - start_time
            logger.info(f"增量计算完成: 总日期 {len(dates_to_calculate)}, 成功 {calculated_dates}, 失败 {failed_dates}, 耗时 {elapsed_time:.2f}s")
            
            return {
                'success': True,
                'total_dates': len(dates_to_calculate),
                'calculated_dates': calculated_dates,
                'skipped_dates': 0,
                'failed_dates': failed_dates,
                'results': results,
                'elapsed_time': elapsed_time
            }
            
        except Exception as e:
            logger.error(f"增量计算指标失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'elapsed_time': time.time() - start_time
            }
    
    def calculate_indicators_for_symbols(self, symbols: List[str], 
                                       start_date: str = None,
                                       end_date: str = None,
                                       force_recalculate: bool = False) -> Dict[str, Any]:
        """为指定股票计算指标
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            force_recalculate: 是否强制重新计算
            
        Returns:
            计算结果统计
        """
        start_time = time.time()
        logger.info(f"开始为 {len(symbols)} 只股票计算指标")
        
        try:
            # 分批并行计算
            calculated_count = 0
            failed_count = 0
            
            # 将股票分组
            symbol_chunks = [symbols[i:i + self.chunk_size] 
                           for i in range(0, len(symbols), self.chunk_size)]
            
            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交任务
                future_to_chunk = {}
                for chunk in symbol_chunks:
                    future = executor.submit(
                        _calculate_indicators_for_symbol_chunk,
                        chunk, start_date, end_date, force_recalculate
                    )
                    future_to_chunk[future] = chunk
                
                # 收集结果
                for future in as_completed(future_to_chunk):
                    chunk = future_to_chunk[future]
                    try:
                        chunk_result = future.result()
                        calculated_count += chunk_result['calculated']
                        failed_count += chunk_result['failed']
                        logger.debug(f"完成计算股票块: {len(chunk)} 只股票")
                    except Exception as e:
                        failed_count += len(chunk)
                        logger.error(f"处理股票块异常: {e}")
            
            elapsed_time = time.time() - start_time
            logger.info(f"股票指标计算完成: 成功 {calculated_count}, 失败 {failed_count}, 耗时 {elapsed_time:.2f}s")
            
            return {
                'success': True,
                'total_symbols': len(symbols),
                'calculated': calculated_count,
                'failed': failed_count,
                'elapsed_time': elapsed_time
            }
            
        except Exception as e:
            logger.error(f"计算股票指标失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'elapsed_time': time.time() - start_time
            }
    
    def _get_trading_dates(self, start_date: str, end_date: str) -> List[str]:
        """获取交易日期列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            交易日期列表
        """
        try:
            # 简单实现：获取日期范围内有数据的日期
            dates = []
            current_date = datetime.strptime(start_date, '%Y-%m-%d')
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
            
            while current_date <= end_date_obj:
                date_str = current_date.strftime('%Y-%m-%d')
                
                # 检查是否有数据
                daily_data = self.data_service.load_daily_data(date_str)
                if not daily_data.empty:
                    dates.append(date_str)
                
                current_date += timedelta(days=1)
            
            return dates
            
        except Exception as e:
            logger.error(f"获取交易日期失败: {e}")
            return []


def _calculate_indicators_for_symbols_chunk(chunk_data: pd.DataFrame, date: str) -> Dict[str, Any]:
    """计算股票块的指标（用于多进程）
    
    Args:
        chunk_data: 股票数据块
        date: 日期
        
    Returns:
        计算结果
    """
    try:
        engine = IndicatorEngine()
        all_indicators = []
        
        # 按股票分组计算
        for symbol, symbol_data in chunk_data.groupby('symbol'):
            try:
                # 计算指标
                indicators = engine.calculate_all_indicators(symbol_data)
                
                # 只保留最新一行数据（当日数据）
                if not indicators.empty:
                    latest_row = indicators.iloc[-1:].copy()
                    latest_row['symbol'] = symbol
                    all_indicators.append(latest_row)
                    
            except Exception as e:
                logger.error(f"计算股票 {symbol} 指标失败: {e}")
                continue
        
        if all_indicators:
            combined_data = pd.concat(all_indicators, ignore_index=True)
            return {
                'success': True,
                'data': combined_data
            }
        else:
            return {
                'success': False,
                'error': '没有成功计算的指标'
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def _calculate_indicators_for_symbol_chunk(symbols: List[str], start_date: str = None,
                                         end_date: str = None, 
                                         force_recalculate: bool = False) -> Dict[str, Any]:
    """计算股票块的指标（用于多进程）
    
    Args:
        symbols: 股票代码列表
        start_date: 开始日期
        end_date: 结束日期
        force_recalculate: 是否强制重新计算
        
    Returns:
        计算结果
    """
    try:
        engine = IndicatorEngine()
        storage = ParquetIndicatorStorage()
        data_service = DataService()
        
        calculated_count = 0
        failed_count = 0
        
        for symbol in symbols:
            try:
                # 检查是否需要重新计算
                if not force_recalculate:
                    existing_data = storage.load_indicators_by_symbol(symbol, start_date, end_date)
                    if not existing_data.empty:
                        continue
                
                # 加载股票数据
                stock_data = data_service.load_stock_history(symbol, start_date, end_date)
                if stock_data.empty:
                    failed_count += 1
                    continue
                
                # 计算指标
                indicators = engine.calculate_all_indicators(stock_data)
                if indicators.empty:
                    failed_count += 1
                    continue
                
                # 保存指标
                save_success = storage.save_indicators_by_symbol(symbol, indicators)
                if save_success:
                    calculated_count += 1
                else:
                    failed_count += 1
                    
            except Exception as e:
                logger.error(f"处理股票 {symbol} 失败: {e}")
                failed_count += 1
        
        return {
            'calculated': calculated_count,
            'failed': failed_count
        }
        
    except Exception as e:
        return {
            'calculated': 0,
            'failed': len(symbols)
        }
