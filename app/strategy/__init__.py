"""
量化分析系统

这是一个专业的量化分析系统，提供以下核心功能：
1. 数据采集与存储
2. 技术指标计算
3. 策略筛选与信号生成
4. K线形态识别
5. 定时复盘分析

系统架构：
- data/: 数据层，负责数据采集、清洗、存储
- indicators/: 指标层，负责技术指标计算
- strategies/: 策略层，负责买卖信号生成
- patterns/: 形态层，负责K线形态识别
- analysis/: 分析层，负责复盘分析
- api/: 接口层，提供对外API服务
- tasks/: 任务层，负责定时任务调度
- utils/: 工具层，提供通用工具函数
"""

__version__ = "1.0.0"
__author__ = "Stock Strategy Team"

# 导出主要模块
from .data import DataCollector, DataManager
from .indicators import IndicatorEngine
from .strategies import StrategyEngine
from .patterns import PatternEngine
from .analysis import AnalysisEngine

__all__ = [
    "DataCollector",
    "DataManager",
    "IndicatorEngine",
    "StrategyEngine",
    "PatternEngine",
    "AnalysisEngine"
]