"""
离线指标计算和存储系统

预计算所有股票的技术指标并存储到DuckDB中，提高在线查询性能
"""
import os
import sys
import time
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import pandas as pd
import numpy as np
import duckdb
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.indicators.engine import IndicatorEngine
from app.strategy.services.data_service import data_service
from app.strategy.config import settings

logger = logging.getLogger(__name__)

class OfflineIndicatorCalculator:
    """离线指标计算器"""
    
    def __init__(self):
        self.indicator_engine = IndicatorEngine()
        self.db_path = Path(settings.data.processed_data_path) / "indicators.duckdb"
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # 延迟初始化数据库连接，避免在子进程中产生锁冲突
        self.conn = None
        self._db_initialized = False
        self._process_id = None  # 记录创建连接的进程ID
    
    def _ensure_db_connection(self):
        """确保数据库连接已建立（进程安全）"""
        import os
        current_pid = os.getpid()

        # 如果连接不存在、未初始化或者进程ID不匹配，重新创建连接
        if (not self.conn or not self._db_initialized or
            self._process_id != current_pid):

            # 关闭旧连接
            if self.conn:
                try:
                    self.conn.close()
                except:
                    pass

            self.conn = duckdb.connect(str(self.db_path))
            self._process_id = current_pid
            self._init_database()
            self._db_initialized = True
            logger.debug(f"数据库连接已建立 (PID: {current_pid}): {self.db_path}")

    def _init_database(self):
        """初始化数据库表结构"""
        try:
            # 使用统一的表结构创建方法
            self._create_indicators_table(self.conn)
            
            # 创建指标摘要表
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS indicator_summary (
                    symbol VARCHAR PRIMARY KEY,
                    latest_date DATE,
                    close DOUBLE,
                    pct_change DOUBLE,
                    -- RSI状态
                    rsi_value DOUBLE,
                    rsi_level VARCHAR,
                    -- MACD状态
                    macd_value DOUBLE,
                    macd_signal DOUBLE,
                    macd_trend VARCHAR,
                    -- KDJ状态
                    kdj_k DOUBLE,
                    kdj_d DOUBLE,
                    kdj_j DOUBLE,
                    kdj_signal VARCHAR,
                    -- 布林带状态
                    boll_upper DOUBLE,
                    boll_middle DOUBLE,
                    boll_lower DOUBLE,
                    boll_position VARCHAR,
                    -- 更新时间
                    updated_at TIMESTAMP
                )
            """)
            
            # 创建索引
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_indicators_symbol ON stock_indicators(symbol)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_indicators_date ON stock_indicators(date)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_summary_symbol ON indicator_summary(symbol)")
            
            logger.info("数据库表结构初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _should_update_symbol_internal(self, symbol: str) -> bool:
        """内部方法：判断是否需要更新股票数据"""
        try:
            self._ensure_db_connection()
            latest_date = self.conn.execute(
                "SELECT MAX(date) FROM stock_indicators WHERE symbol = ?",
                [symbol]
            ).fetchone()

            if not latest_date or not latest_date[0]:
                return True

            # 如果最新数据超过1天，则需要更新
            from datetime import datetime
            today = datetime.now().date()
            latest = latest_date[0]

            if isinstance(latest, str):
                latest = datetime.strptime(latest, '%Y-%m-%d').date()

            return (today - latest).days > 0

        except Exception:
            return True

    def calculate_single_stock_indicators(self, symbol: str,
                                        start_date: str = None,
                                        end_date: str = None) -> bool:
        """计算单只股票的指标"""
        try:
            print(f"  计算股票 {symbol} 的指标...")
            
            # 加载股票数据
            stock_data = data_service.load_stock_history(symbol, start_date, end_date, limit=500)
            
            if stock_data.empty:
                print(f"    ⚠ 股票 {symbol} 无数据")
                return False
            
            # 数据验证和清洗
            if not self._validate_stock_data(stock_data):
                print(f"    ⚠ 股票 {symbol} 数据验证失败")
                return False
            
            # 计算所有指标
            indicators_data = self.indicator_engine.calculate_all_indicators(stock_data)
            
            if indicators_data.empty:
                print(f"    ⚠ 股票 {symbol} 指标计算失败")
                return False
            
            # 保存到数据库
            self._save_indicators_to_db(symbol, indicators_data, start_date)

            # 更新摘要
            self._update_indicator_summary(symbol, indicators_data)
            
            print(f"    ✓ 股票 {symbol} 指标计算完成: {len(indicators_data)} 条记录")
            return True
            
        except Exception as e:
            print(f"    ❌ 股票 {symbol} 指标计算失败: {e}")
            logger.error(f"计算股票 {symbol} 指标失败: {e}")
            return False
    
    def _validate_stock_data(self, data: pd.DataFrame) -> bool:
        """验证股票数据质量"""
        try:
            # 检查必要字段
            required_fields = ['symbol', 'date', 'open', 'high', 'low', 'close', 'volume']
            missing_fields = [field for field in required_fields if field not in data.columns]
            
            if missing_fields:
                logger.warning(f"缺少必要字段: {missing_fields}")
                return False
            
            # 检查数据量
            if len(data) < 20:
                logger.warning(f"数据量不足: {len(data)} 条")
                return False
            
            # 检查数据类型
            for col in ['open', 'high', 'low', 'close', 'volume']:
                if not pd.api.types.is_numeric_dtype(data[col]):
                    logger.warning(f"字段 {col} 不是数值类型")
                    return False
            
            # 检查价格逻辑
            invalid_prices = (
                (data['high'] < data['low']) |
                (data['high'] < data['open']) |
                (data['high'] < data['close']) |
                (data['low'] > data['open']) |
                (data['low'] > data['close']) |
                (data['close'] <= 0)
            ).sum()
            
            if invalid_prices > len(data) * 0.1:  # 超过10%的数据有问题
                logger.warning(f"价格数据异常比例过高: {invalid_prices}/{len(data)}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return False
    
    def _save_indicators_to_db(self, symbol: str, data: pd.DataFrame, start_date: str = None):
        """保存指标数据到数据库"""
        try:
            # 准备数据
            save_data = data.copy()

            # 确保日期格式正确
            save_data['date'] = pd.to_datetime(save_data['date']).dt.date
            save_data['calculated_at'] = datetime.now()

            # 动态创建表结构（如果需要）
            self._ensure_table_structure(save_data)

            # 删除已存在的数据（支持增量更新）
            if start_date:
                # 增量模式：只删除指定日期范围的数据
                self.conn.execute(
                    "DELETE FROM stock_indicators WHERE symbol = ? AND date >= ?",
                    [symbol, start_date]
                )
            else:
                # 全量模式：删除该股票的所有数据
                self.conn.execute(
                    "DELETE FROM stock_indicators WHERE symbol = ? AND date >= ? AND date <= ?",
                    [symbol, save_data['date'].min(), save_data['date'].max()]
                )

            # 插入新数据
            self.conn.register('temp_indicators', save_data)
            self.conn.execute("INSERT INTO stock_indicators SELECT * FROM temp_indicators")
            self.conn.unregister('temp_indicators')

        except Exception as e:
            logger.error(f"保存指标数据失败: {e}")
            raise

    def _ensure_table_structure(self, data: pd.DataFrame):
        """确保表结构与数据匹配"""
        try:
            # 获取当前表的列
            try:
                current_columns = self.conn.execute("PRAGMA table_info(stock_indicators)").fetchall()
                current_column_names = [col[1] for col in current_columns]
            except:
                current_column_names = []

            # 获取数据中的列
            data_columns = list(data.columns)

            # 如果表不存在或列不匹配，重新创建表
            missing_columns = [col for col in data_columns if col not in current_column_names]

            if missing_columns or not current_column_names:
                logger.info(f"重新创建表结构，新增列: {missing_columns}")

                # 删除旧表
                self.conn.execute("DROP TABLE IF EXISTS stock_indicators")

                # 动态创建新表
                column_definitions = []
                for col in data_columns:
                    if col in ['symbol']:
                        column_definitions.append(f"{col} VARCHAR")
                    elif col in ['date']:
                        column_definitions.append(f"{col} DATE")
                    elif col in ['calculated_at']:
                        column_definitions.append(f"{col} TIMESTAMP")
                    elif col.endswith('_direction') or col.endswith('_level') or col.endswith('_signal') or col.endswith('_trend') or col.endswith('_position'):
                        # 字符串类型的指标字段
                        column_definitions.append(f"{col} VARCHAR")
                    elif col.startswith('pattern_') or col.startswith('wave_'):
                        # 布尔类型的指标字段
                        column_definitions.append(f"{col} BOOLEAN")
                    else:
                        column_definitions.append(f"{col} DOUBLE")

                create_sql = f"""
                    CREATE TABLE stock_indicators (
                        {', '.join(column_definitions)},
                        PRIMARY KEY (symbol, date)
                    )
                """

                self.conn.execute(create_sql)

                # 重新创建索引
                self.conn.execute("CREATE INDEX IF NOT EXISTS idx_indicators_symbol ON stock_indicators(symbol)")
                self.conn.execute("CREATE INDEX IF NOT EXISTS idx_indicators_date ON stock_indicators(date)")

        except Exception as e:
            logger.error(f"确保表结构失败: {e}")
            raise
    
    def _update_indicator_summary(self, symbol: str, data: pd.DataFrame):
        """更新指标摘要"""
        try:
            if data.empty:
                return
            
            latest = data.iloc[-1]
            
            # 计算RSI状态
            rsi_value = latest.get('RSI', np.nan)
            rsi_level = 'normal'
            if pd.notna(rsi_value):
                if rsi_value > 70:
                    rsi_level = 'overbought'
                elif rsi_value < 30:
                    rsi_level = 'oversold'
            
            # 计算MACD趋势
            macd_value = latest.get('MACD', np.nan)
            macd_signal = latest.get('MACD_Signal', np.nan)
            macd_trend = 'neutral'
            if pd.notna(macd_value) and pd.notna(macd_signal):
                macd_trend = 'bullish' if macd_value > macd_signal else 'bearish'
            
            # 计算KDJ信号
            kdj_k = latest.get('KDJ_K', np.nan)
            kdj_d = latest.get('KDJ_D', np.nan)
            kdj_signal = 'neutral'
            if pd.notna(kdj_k) and pd.notna(kdj_d):
                kdj_signal = 'golden_cross' if kdj_k > kdj_d else 'death_cross'
            
            # 计算布林带位置
            close_price = latest.get('close', np.nan)
            boll_upper = latest.get('BOLL_Upper', np.nan)
            boll_lower = latest.get('BOLL_Lower', np.nan)
            boll_position = 'middle'
            if pd.notna(close_price) and pd.notna(boll_upper) and pd.notna(boll_lower):
                if close_price > boll_upper:
                    boll_position = 'upper'
                elif close_price < boll_lower:
                    boll_position = 'lower'
            
            # 准备摘要数据
            summary_data = {
                'symbol': symbol,
                'latest_date': latest.get('date'),
                'close': latest.get('close', np.nan),
                'pct_change': latest.get('pct_change', np.nan),
                'rsi_value': rsi_value,
                'rsi_level': rsi_level,
                'macd_value': macd_value,
                'macd_signal': macd_signal,
                'macd_trend': macd_trend,
                'kdj_k': kdj_k,
                'kdj_d': kdj_d,
                'kdj_j': latest.get('KDJ_J', np.nan),
                'kdj_signal': kdj_signal,
                'boll_upper': boll_upper,
                'boll_middle': latest.get('BOLL_Middle', np.nan),
                'boll_lower': boll_lower,
                'boll_position': boll_position,
                'updated_at': datetime.now()
            }
            
            # 删除已存在的摘要
            self.conn.execute("DELETE FROM indicator_summary WHERE symbol = ?", [symbol])
            
            # 插入新摘要
            columns = list(summary_data.keys())
            placeholders = ', '.join(['?' for _ in columns])
            values = [summary_data[col] for col in columns]
            
            self.conn.execute(
                f"INSERT INTO indicator_summary ({', '.join(columns)}) VALUES ({placeholders})",
                values
            )
            
        except Exception as e:
            logger.error(f"更新指标摘要失败: {e}")
    
    def batch_calculate_indicators(self, symbols: List[str] = None, 
                                 max_workers: int = 4, 
                                 batch_size: int = 100) -> Dict:
        """批量计算指标"""
        print("🚀 开始批量计算指标...")
        
        if symbols is None:
            stock_list = data_service.get_stock_list()
            symbols = [stock['symbol'] for stock in stock_list]
        
        print(f"📊 需要计算: {len(symbols)} 只股票")
        
        start_time = time.time()
        success_count = 0
        failed_count = 0
        
        # 分批处理
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(symbols) + batch_size - 1) // batch_size
            
            print(f"\n📦 处理批次 {batch_num}/{total_batches} ({len(batch_symbols)} 只股票)")
            
            # 并行计算
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {
                    executor.submit(self.calculate_single_stock_indicators, symbol): symbol
                    for symbol in batch_symbols
                }
                
                for future in as_completed(futures):
                    symbol = futures[future]
                    try:
                        success = future.result()
                        if success:
                            success_count += 1
                        else:
                            failed_count += 1
                    except Exception as e:
                        failed_count += 1
                        print(f"    ❌ {symbol}: {e}")
            
            # 批次间休息
            if batch_num < total_batches:
                print(f"  ⏸ 批次完成，休息 2 秒...")
                time.sleep(2)
        
        elapsed_time = time.time() - start_time
        
        print(f"\n🎉 批量计算完成!")
        print(f"📊 总计: {len(symbols)} 只股票")
        print(f"✅ 成功: {success_count} 只")
        print(f"❌ 失败: {failed_count} 只")
        print(f"⏱ 耗时: {elapsed_time:.1f} 秒")
        print(f"📈 成功率: {success_count/len(symbols)*100:.1f}%")
        
        return {
            'total': len(symbols),
            'success': success_count,
            'failed': failed_count,
            'elapsed_time': elapsed_time,
            'success_rate': success_count / len(symbols) if symbols else 0
        }
    
    def get_stock_indicators_from_db(self, symbol: str,
                                   start_date: str = None,
                                   end_date: str = None,
                                   limit: int = 250) -> pd.DataFrame:
        """从数据库获取股票指标"""
        try:
            self._ensure_db_connection()
            query = "SELECT * FROM stock_indicators WHERE symbol = ?"
            params = [symbol]

            if start_date:
                query += " AND date >= ?"
                params.append(start_date)

            if end_date:
                query += " AND date <= ?"
                params.append(end_date)

            query += " ORDER BY date DESC"

            if limit:
                query += f" LIMIT {limit}"

            result = self.conn.execute(query, params).fetchdf()

            if not result.empty:
                # 按日期正序排列
                result = result.sort_values('date')
                result['date'] = pd.to_datetime(result['date'])

            return result

        except Exception as e:
            logger.error(f"从数据库获取指标失败: {e}")
            return pd.DataFrame()

    def _create_dynamic_table(self, conn, data: pd.DataFrame):
        """根据数据动态创建表结构"""
        try:
            # 检查表是否已存在
            tables = conn.execute("SHOW TABLES").fetchall()
            table_names = [table[0] for table in tables]

            if 'stock_indicators' in table_names:
                # 表已存在，检查是否需要添加新列
                existing_columns = conn.execute("PRAGMA table_info(stock_indicators)").fetchall()
                existing_column_names = [col[1] for col in existing_columns]

                # 添加缺失的列
                for col in data.columns:
                    if col not in existing_column_names:
                        col_type = self._get_column_type(data[col])
                        try:
                            conn.execute(f"ALTER TABLE stock_indicators ADD COLUMN {col} {col_type}")
                            logger.info(f"添加新列: {col} {col_type}")
                        except Exception as e:
                            logger.warning(f"添加列 {col} 失败: {e}")
            else:
                # 创建新表
                column_definitions = []
                for col in data.columns:
                    col_type = self._get_column_type(data[col])
                    column_definitions.append(f"{col} {col_type}")

                # 添加主键约束
                if 'symbol' in data.columns and 'date' in data.columns:
                    primary_key = "PRIMARY KEY (symbol, date)"
                else:
                    primary_key = ""

                create_sql = f"""
                    CREATE TABLE stock_indicators (
                        {', '.join(column_definitions)}
                        {', ' + primary_key if primary_key else ''}
                    )
                """

                conn.execute(create_sql)
                logger.info("动态创建股票指标表")

        except Exception as e:
            logger.error(f"动态创建表失败: {e}")
            raise

    def _get_column_type(self, series: pd.Series) -> str:
        """根据pandas Series确定DuckDB列类型"""
        if series.dtype == 'object':
            # 检查是否是日期字符串
            if series.name in ['date']:
                return 'DATE'
            elif series.name in ['calculated_at']:
                return 'TIMESTAMP'
            else:
                return 'VARCHAR'
        elif pd.api.types.is_datetime64_any_dtype(series):
            return 'TIMESTAMP'
        elif pd.api.types.is_bool_dtype(series):
            return 'BOOLEAN'
        elif pd.api.types.is_integer_dtype(series):
            return 'BIGINT'
        elif pd.api.types.is_float_dtype(series):
            return 'DOUBLE'
        else:
            return 'VARCHAR'
    
    def get_indicator_summary_from_db(self, symbol: str) -> Dict:
        """从数据库获取指标摘要"""
        try:
            self._ensure_db_connection()
            result = self.conn.execute(
                "SELECT * FROM indicator_summary WHERE symbol = ?",
                [symbol]
            ).fetchone()

            if result:
                columns = [desc[0] for desc in self.conn.description]
                return dict(zip(columns, result))
            else:
                return {}

        except Exception as e:
            logger.error(f"从数据库获取指标摘要失败: {e}")
            return {}
    
    def _should_update_symbol_safe(self, symbol: str) -> bool:
        """进程安全的检查是否需要更新股票数据"""
        import time
        import random

        max_retries = 3
        retry_delay = 0.1

        for attempt in range(max_retries):
            try:
                # 使用只读连接，避免锁冲突
                import duckdb
                with duckdb.connect(str(self.db_path), read_only=True) as readonly_conn:
                    result = readonly_conn.execute(
                        "SELECT MAX(date) FROM stock_indicators WHERE symbol = ?",
                        [symbol]
                    ).fetchone()

                    latest_date = result[0] if result and result[0] else None
                    if not latest_date:
                        return True

                    # 如果最新数据超过1天，则需要更新
                    from datetime import datetime
                    today = datetime.now().date()
                    if isinstance(latest_date, str):
                        latest_date = datetime.strptime(latest_date, '%Y-%m-%d').date()

                    return (today - latest_date).days > 0

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"检查股票 {symbol} 更新状态失败，重试 {attempt + 1}/{max_retries}: {e}")
                    wait_time = retry_delay * (2 ** attempt) + random.uniform(0, 0.1)
                    time.sleep(wait_time)
                    continue
                else:
                    # 如果检查失败，默认需要更新
                    logger.warning(f"检查股票 {symbol} 更新状态失败: {e}")
                    return True

    def calculate_single_stock_indicators_safe(self, symbol: str, start_date: str = None) -> bool:
        """进程安全的计算单只股票指标"""
        try:
            import os
            import time
            import tempfile
            import shutil

            # 获取股票数据 - 直接从文件读取避免数据库锁冲突
            data = self._load_stock_data_from_files(symbol, start_date)
            if data.empty:
                logger.warning(f"股票 {symbol} 数据为空")
                return False

            # 计算指标
            indicators_data = self.indicator_engine.calculate_all_indicators(data)
            if indicators_data.empty:
                logger.warning(f"股票 {symbol} 指标计算失败")
                return False

            # 使用临时文件和原子操作保存数据
            self._save_indicators_to_db_safe(symbol, indicators_data, start_date)

            logger.info(f"股票 {symbol} 指标计算完成，共 {len(indicators_data)} 条记录")
            return True

        except Exception as e:
            logger.error(f"计算股票 {symbol} 指标失败: {e}")
            return False

    def _save_indicators_to_db_safe(self, symbol: str, data: pd.DataFrame, start_date: str = None):
        """进程安全的保存指标数据到数据库"""
        import os
        import time
        import random
        import tempfile
        import shutil
        from pathlib import Path

        max_retries = 10
        retry_delay = 0.2

        for attempt in range(max_retries):
            try:
                # 准备数据
                save_data = data.copy()
                save_data['date'] = pd.to_datetime(save_data['date']).dt.date
                save_data = self._prepare_data_for_db(save_data)

                # 使用临时数据库文件进行写入，然后原子性替换
                temp_db_path = self.db_path.with_suffix(f'.tmp_{os.getpid()}_{attempt}')

                try:
                    # 如果主数据库存在，先复制到临时文件
                    if self.db_path.exists():
                        shutil.copy2(self.db_path, temp_db_path)

                    # 在临时数据库上操作
                    import duckdb
                    with duckdb.connect(str(temp_db_path)) as temp_conn:
                        # 动态创建表结构
                        self._create_dynamic_table(temp_conn, save_data)

                        # 删除已存在的数据（删除该股票的所有数据）
                        temp_conn.execute(
                            "DELETE FROM stock_indicators WHERE symbol = ?",
                            [symbol]
                        )

                        # 插入新数据
                        temp_conn.register('temp_data', save_data)
                        temp_conn.execute("INSERT INTO stock_indicators SELECT * FROM temp_data")
                        temp_conn.unregister('temp_data')

                    # 原子性替换主数据库文件
                    if temp_db_path.exists():
                        # 使用原子性移动操作
                        backup_path = self.db_path.with_suffix('.backup')
                        if self.db_path.exists():
                            shutil.move(self.db_path, backup_path)

                        shutil.move(temp_db_path, self.db_path)

                        # 删除备份文件
                        if backup_path.exists():
                            backup_path.unlink()

                    logger.debug(f"股票 {symbol} 指标数据已保存到数据库")
                    return

                finally:
                    # 清理临时文件
                    if temp_db_path.exists():
                        try:
                            temp_db_path.unlink()
                        except:
                            pass

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"保存数据失败，重试 {attempt + 1}/{max_retries}: {e}")
                    wait_time = retry_delay * (2 ** min(attempt, 3)) + random.uniform(0, 0.1)
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"保存指标数据失败: {e}")
                    raise

    def _prepare_data_for_db(self, data: pd.DataFrame) -> pd.DataFrame:
        """准备数据用于数据库存储"""
        result = data.copy()

        # 添加计算时间列
        result['calculated_at'] = datetime.now()

        # 确保必需的基础列存在
        if 'symbol' not in result.columns:
            result['symbol'] = ''
        if 'date' not in result.columns:
            result['date'] = pd.NaT
        if 'close' not in result.columns:
            result['close'] = np.nan

        return result

    def _ensure_table_structure_safe(self, conn, data: pd.DataFrame):
        """进程安全的确保表结构存在"""
        try:
            # 使用动态表创建
            self._create_dynamic_table(conn, data)
        except Exception as e:
            logger.error(f"确保表结构失败: {e}")
            raise

    def _create_indicators_table(self, conn):
        """创建指标数据表"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS stock_indicators (
                symbol VARCHAR,
                date DATE,
                close DOUBLE,
                -- 趋势指标
                MACD DOUBLE,
                MACD_Signal DOUBLE,
                MACD_Histogram DOUBLE,
                BOLL_Upper DOUBLE,
                BOLL_Middle DOUBLE,
                BOLL_Lower DOUBLE,
                MA_5 DOUBLE,
                MA_10 DOUBLE,
                MA_20 DOUBLE,
                MA_60 DOUBLE,
                EMA_12 DOUBLE,
                EMA_26 DOUBLE,
                TRIX DOUBLE,
                SMA_20 DOUBLE,
                DMA DOUBLE,
                AMA DOUBLE,
                -- 震荡指标
                RSI_14 DOUBLE,
                KDJ_K DOUBLE,
                KDJ_D DOUBLE,
                KDJ_J DOUBLE,
                CCI_14 DOUBLE,
                WR_14 DOUBLE,
                ROC_12 DOUBLE,
                PSY_12 DOUBLE,
                BIAS_6 DOUBLE,
                BIAS_12 DOUBLE,
                BIAS_24 DOUBLE,
                -- 成交量指标
                OBV DOUBLE,
                VR_26 DOUBLE,
                MAVR DOUBLE,
                MFI_14 DOUBLE,
                VWMA_20 DOUBLE,
                -- 动量指标
                DMI_14 DOUBLE,
                PDI_14 DOUBLE,
                MDI_14 DOUBLE,
                DX_14 DOUBLE,
                ADX_14 DOUBLE,
                ADXR_14 DOUBLE,
                -- 波动指标
                TR DOUBLE,
                ATR_14 DOUBLE,
                SAR DOUBLE,
                BRAR_26 DOUBLE,
                EMV_14 DOUBLE,
                -- 其他指标
                CR_26 DOUBLE,
                TEMA_30 DOUBLE,
                PPO DOUBLE,
                WT1 DOUBLE,
                WT2 DOUBLE,
                Supertrend DOUBLE,
                DPO_20 DOUBLE,
                VHF_28 DOUBLE,
                RVI_10 DOUBLE,
                FI_13 DOUBLE,
                ENE_Upper DOUBLE,
                ENE_Lower DOUBLE,
                STOCHRSI_K DOUBLE,
                STOCHRSI_D DOUBLE,
                calculated_at TIMESTAMP,
                PRIMARY KEY (symbol, date)
            )
        """)

    def _load_stock_data_from_files(self, symbol: str, start_date: str = None) -> pd.DataFrame:
        """直接从parquet文件读取股票数据，避免数据库锁冲突"""
        try:
            from pathlib import Path
            import pandas as pd
            from datetime import datetime, timedelta

            # 构建数据文件路径
            data_root = Path(settings.data.raw_data_path)
            all_data = []

            # 如果指定了开始日期，只读取该日期之后的数据
            if start_date:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                # 向前多读取一些数据以确保技术指标计算的准确性
                start_dt = start_dt - timedelta(days=100)
            else:
                # 默认读取最近一年的数据
                start_dt = datetime.now() - timedelta(days=365)

            # 遍历年月目录，数据是按日期存储的
            for year_dir in data_root.iterdir():
                if not year_dir.is_dir() or not year_dir.name.isdigit():
                    continue

                year = int(year_dir.name)
                if year < start_dt.year:
                    continue

                for month_dir in year_dir.iterdir():
                    if not month_dir.is_dir() or not month_dir.name.isdigit():
                        continue

                    month = int(month_dir.name)
                    if year == start_dt.year and month < start_dt.month:
                        continue

                    # 遍历该月的所有日期文件
                    for date_file in month_dir.glob("*.parquet"):
                        try:
                            # 解析日期
                            date_str = date_file.stem  # 例如: 20250127
                            file_date = datetime.strptime(date_str, '%Y%m%d')

                            # 检查日期是否在范围内
                            if file_date < start_dt:
                                continue

                            # 读取文件并过滤指定股票
                            daily_data = pd.read_parquet(date_file)
                            if 'symbol' in daily_data.columns:
                                stock_data = daily_data[daily_data['symbol'] == symbol]
                                if not stock_data.empty:
                                    all_data.append(stock_data)

                        except Exception as e:
                            logger.warning(f"读取文件 {date_file} 失败: {e}")
                            continue

            if not all_data:
                logger.warning(f"未找到股票 {symbol} 的数据文件")
                return pd.DataFrame()

            # 合并所有数据
            data = pd.concat(all_data, ignore_index=True)

            # 确保日期列存在并排序
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
                data = data.sort_values('date')

                # 如果指定了开始日期，过滤数据
                if start_date:
                    filter_date = datetime.strptime(start_date, '%Y-%m-%d')
                    # 保留开始日期前的一些数据用于技术指标计算
                    keep_date = filter_date - timedelta(days=60)
                    data = data[data['date'] >= keep_date]

            # 去重并重置索引
            data = data.drop_duplicates(subset=['date']).reset_index(drop=True)

            logger.debug(f"从文件读取股票 {symbol} 数据 {len(data)} 条")
            return data

        except Exception as e:
            logger.error(f"从文件读取股票 {symbol} 数据失败: {e}")
            return pd.DataFrame()

    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()

# 注意：不要创建全局实例，在多进程环境中会导致数据库锁冲突
# 请在需要时创建独立的实例：calculator = OfflineIndicatorCalculator()
