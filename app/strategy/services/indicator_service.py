"""
高性能指标计算服务

提供技术指标的快速计算和缓存功能
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from functools import lru_cache
import hashlib
import json
from concurrent.futures import ThreadPoolExecutor
import asyncio

from ..indicators.engine import IndicatorEngine
from .data_service import data_service

logger = logging.getLogger(__name__)

class HighPerformanceIndicatorService:
    """高性能指标计算服务"""
    
    def __init__(self):
        self.indicator_engine = IndicatorEngine()
        self.cache_size = 500
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    def _generate_cache_key(self, symbol: str, start_date: str, end_date: str, 
                          indicators: List[str]) -> str:
        """生成缓存键"""
        key_data = {
            'symbol': symbol,
            'start_date': start_date,
            'end_date': end_date,
            'indicators': sorted(indicators)
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    @lru_cache(maxsize=500)
    def calculate_indicators_cached(self, symbol: str, start_date: str, end_date: str,
                                  indicators_tuple: tuple) -> pd.DataFrame:
        """计算指标（带缓存）"""
        try:
            # 加载股票数据
            data = data_service.load_stock_history(symbol, start_date, end_date)
            
            if data.empty:
                logger.warning(f"股票 {symbol} 无数据")
                return pd.DataFrame()
            
            # 转换为列表
            indicators = list(indicators_tuple)
            
            # 计算指标
            if 'all' in indicators:
                result_data = self.indicator_engine.calculate_all_indicators(data)
            else:
                result_data = data.copy()
                for indicator in indicators:
                    result_data = self._calculate_single_indicator(result_data, indicator)
            
            logger.info(f"股票 {symbol} 指标计算完成: {len(result_data)} 条记录")
            return result_data
            
        except Exception as e:
            logger.error(f"计算股票 {symbol} 指标失败: {e}")
            return pd.DataFrame()
    
    def calculate_indicators(self, symbol: str, start_date: str = None,
                           end_date: str = None, indicators: List[str] = None) -> pd.DataFrame:
        """获取技术指标（仅从数据库读取）"""
        try:
            # 仅从数据库读取，不进行在线计算
            # 创建独立的计算器实例避免多进程冲突
            from app.strategy.services.offline_calculator import OfflineIndicatorCalculator
            calculator = OfflineIndicatorCalculator()
            try:
                db_data = calculator.get_stock_indicators_from_db(symbol, start_date, end_date, limit=250)
            finally:
                calculator.close()

            if not db_data.empty:
                logger.info(f"从数据库读取股票 {symbol} 指标: {len(db_data)} 条记录")
                return db_data
            else:
                logger.warning(f"数据库中无股票 {symbol} 指标数据，请先运行离线计算")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"读取指标失败: {e}")
            return pd.DataFrame()
    
    def _calculate_single_indicator(self, data: pd.DataFrame, indicator: str) -> pd.DataFrame:
        """计算单个指标"""
        try:
            if indicator.upper() == 'MACD':
                return self.indicator_engine._calculate_macd(data)
            elif indicator.upper() == 'RSI':
                return self.indicator_engine._calculate_rsi(data)
            elif indicator.upper() == 'KDJ':
                return self.indicator_engine._calculate_kdj(data)
            elif indicator.upper() == 'BOLL':
                return self.indicator_engine._calculate_boll(data)
            elif indicator.upper() == 'MA':
                return self.indicator_engine._calculate_ma(data)
            elif indicator.upper() == 'EMA':
                return self.indicator_engine._calculate_ema(data)
            elif indicator.upper() == 'CCI':
                return self.indicator_engine._calculate_cci(data)
            elif indicator.upper() == 'WR':
                return self.indicator_engine._calculate_wr(data)
            elif indicator.upper() == 'OBV':
                return self.indicator_engine._calculate_obv(data)
            elif indicator.upper() == 'VOL_MA':
                return self.indicator_engine._calculate_vol_ma(data)
            else:
                logger.warning(f"未知指标: {indicator}")
                return data
                
        except Exception as e:
            logger.error(f"计算指标 {indicator} 失败: {e}")
            return data
    
    async def calculate_batch_indicators(self, symbols: List[str], 
                                       indicators: List[str] = None,
                                       limit: int = 50) -> Dict[str, pd.DataFrame]:
        """批量计算指标"""
        try:
            if indicators is None:
                indicators = ['MACD', 'RSI', 'KDJ', 'BOLL', 'MA']
            
            # 限制并发数量
            symbols = symbols[:limit]
            
            # 并行计算
            loop = asyncio.get_event_loop()
            tasks = []
            
            for symbol in symbols:
                task = loop.run_in_executor(
                    self.executor,
                    self.calculate_indicators,
                    symbol, None, None, indicators
                )
                tasks.append((symbol, task))
            
            results = {}
            for symbol, task in tasks:
                try:
                    result = await task
                    if not result.empty:
                        results[symbol] = result
                except Exception as e:
                    logger.warning(f"计算股票 {symbol} 指标失败: {e}")
            
            logger.info(f"批量计算完成: {len(results)}/{len(symbols)} 只股票")
            return results
            
        except Exception as e:
            logger.error(f"批量计算指标失败: {e}")
            return {}
    
    def get_indicator_summary(self, symbol: str, indicators: List[str] = None) -> Dict:
        """获取指标摘要（优先从数据库读取）"""
        try:
            # 首先尝试从数据库读取摘要
            from app.strategy.services.offline_calculator import OfflineIndicatorCalculator
            calculator = OfflineIndicatorCalculator()
            try:
                db_summary = calculator.get_indicator_summary_from_db(symbol)
            finally:
                calculator.close()

            if db_summary:
                # 转换数据库格式为API格式
                summary = {
                    'symbol': symbol,
                    'date': db_summary.get('latest_date').strftime('%Y-%m-%d') if db_summary.get('latest_date') else None,
                    'close': float(db_summary.get('close', 0)),
                    'pct_change': float(db_summary.get('pct_change', 0)),
                    'indicators': {}
                }

                # RSI指标
                if db_summary.get('rsi_value') is not None:
                    summary['indicators']['RSI'] = {
                        'value': float(db_summary['rsi_value']),
                        'level': db_summary.get('rsi_level', 'normal')
                    }

                # MACD指标
                if db_summary.get('macd_value') is not None:
                    summary['indicators']['MACD'] = {
                        'value': float(db_summary['macd_value']),
                        'signal': float(db_summary.get('macd_signal', 0)),
                        'trend': db_summary.get('macd_trend', 'neutral')
                    }

                # KDJ指标
                if db_summary.get('kdj_k') is not None:
                    summary['indicators']['KDJ'] = {
                        'K': float(db_summary['kdj_k']),
                        'D': float(db_summary.get('kdj_d', 0)),
                        'J': float(db_summary.get('kdj_j', 0)),
                        'signal': db_summary.get('kdj_signal', 'neutral')
                    }

                # 布林带指标
                if db_summary.get('boll_upper') is not None:
                    summary['indicators']['BOLL'] = {
                        'upper': float(db_summary['boll_upper']),
                        'middle': float(db_summary.get('boll_middle', 0)),
                        'lower': float(db_summary.get('boll_lower', 0)),
                        'position': db_summary.get('boll_position', 'middle'),
                        'width': (float(db_summary['boll_upper']) - float(db_summary.get('boll_lower', 0))) / float(db_summary.get('boll_middle', 1)) if db_summary.get('boll_middle', 0) > 0 else 0.0
                    }

                logger.info(f"从数据库读取股票 {symbol} 指标摘要")
                return summary

            # 如果数据库没有摘要，返回空结果
            logger.warning(f"数据库中无股票 {symbol} 指标摘要，请先运行离线计算")
            return {}
            
            # MACD指标
            if 'MACD' in latest.index and pd.notna(latest['MACD']):
                summary['indicators']['MACD'] = {
                    'value': float(latest['MACD']),
                    'signal': float(latest['MACD_Signal']) if 'MACD_Signal' in latest.index else 0.0,
                    'histogram': float(latest['MACD_Histogram']) if 'MACD_Histogram' in latest.index else 0.0,
                    'trend': 'bullish' if latest['MACD'] > latest.get('MACD_Signal', 0) else 'bearish'
                }
            
            # RSI指标
            if 'RSI' in latest.index and pd.notna(latest['RSI']):
                rsi_value = float(latest['RSI'])
                summary['indicators']['RSI'] = {
                    'value': rsi_value,
                    'level': 'overbought' if rsi_value > 70 else 'oversold' if rsi_value < 30 else 'normal'
                }
            
            # KDJ指标
            if 'KDJ_K' in latest.index and pd.notna(latest['KDJ_K']):
                k_value = float(latest['KDJ_K'])
                d_value = float(latest['KDJ_D']) if 'KDJ_D' in latest.index else 0.0
                summary['indicators']['KDJ'] = {
                    'K': k_value,
                    'D': d_value,
                    'J': float(latest['KDJ_J']) if 'KDJ_J' in latest.index else 0.0,
                    'signal': 'golden_cross' if k_value > d_value else 'death_cross'
                }
            
            # 布林带指标
            if 'BOLL_Upper' in latest.index and pd.notna(latest['BOLL_Upper']):
                close_price = float(latest['close'])
                upper = float(latest['BOLL_Upper'])
                lower = float(latest['BOLL_Lower']) if 'BOLL_Lower' in latest.index else 0.0
                middle = float(latest['BOLL_Middle']) if 'BOLL_Middle' in latest.index else 0.0
                
                position = 'upper' if close_price > upper else 'lower' if close_price < lower else 'middle'
                
                summary['indicators']['BOLL'] = {
                    'upper': upper,
                    'middle': middle,
                    'lower': lower,
                    'position': position,
                    'width': (upper - lower) / middle if middle > 0 else 0.0
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取指标摘要失败: {e}")
            return {}
    
    def get_market_indicators_ranking(self, indicator: str = 'RSI', 
                                    limit: int = 100) -> List[Dict]:
        """获取市场指标排行"""
        try:
            # 获取活跃股票列表
            stock_list = data_service.get_top_stocks('amount', limit * 2)
            symbols = [stock['symbol'] for stock in stock_list[:limit]]
            
            rankings = []
            
            for symbol in symbols:
                try:
                    summary = self.get_indicator_summary(symbol, [indicator])
                    
                    if summary and 'indicators' in summary and indicator in summary['indicators']:
                        indicator_data = summary['indicators'][indicator]
                        
                        ranking_item = {
                            'symbol': symbol,
                            'close': summary['close'],
                            'pct_change': summary['pct_change'],
                            'indicator': indicator,
                            'value': indicator_data.get('value', 0.0)
                        }
                        
                        # 添加特定指标的额外信息
                        if indicator == 'RSI':
                            ranking_item['level'] = indicator_data.get('level', 'normal')
                        elif indicator == 'MACD':
                            ranking_item['trend'] = indicator_data.get('trend', 'neutral')
                        elif indicator == 'KDJ':
                            ranking_item['signal'] = indicator_data.get('signal', 'neutral')
                        
                        rankings.append(ranking_item)
                        
                except Exception as e:
                    logger.warning(f"计算股票 {symbol} 指标排行失败: {e}")
                    continue
            
            # 按指标值排序
            rankings.sort(key=lambda x: x['value'], reverse=True)
            
            logger.info(f"指标 {indicator} 排行计算完成: {len(rankings)} 只股票")
            return rankings
            
        except Exception as e:
            logger.error(f"获取指标排行失败: {e}")
            return []
    
    def get_available_indicators(self) -> List[Dict]:
        """获取可用指标列表"""
        return [
            {'name': 'MACD', 'description': 'MACD指标', 'category': 'trend'},
            {'name': 'RSI', 'description': 'RSI相对强弱指标', 'category': 'momentum'},
            {'name': 'KDJ', 'description': 'KDJ随机指标', 'category': 'momentum'},
            {'name': 'BOLL', 'description': '布林带指标', 'category': 'volatility'},
            {'name': 'MA', 'description': '移动平均线', 'category': 'trend'},
            {'name': 'EMA', 'description': '指数移动平均线', 'category': 'trend'},
            {'name': 'CCI', 'description': 'CCI顺势指标', 'category': 'momentum'},
            {'name': 'WR', 'description': 'WR威廉指标', 'category': 'momentum'},
            {'name': 'OBV', 'description': 'OBV能量潮指标', 'category': 'volume'},
            {'name': 'VOL_MA', 'description': '成交量移动平均', 'category': 'volume'}
        ]

# 全局指标服务实例
indicator_service = HighPerformanceIndicatorService()
