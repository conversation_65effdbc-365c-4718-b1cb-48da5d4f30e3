"""
数据采集器测试

测试数据采集功能的正确性
"""
import os
import sys
import unittest
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.data.miniqmt_collector import MiniQMTCollector as DataCollector
from app.strategy.data.manager import DataManager
from app.strategy.config import settings

class TestDataCollector(unittest.TestCase):
    """数据采集器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.collector = DataCollector()
        self.manager = DataManager()
        
        # 使用测试数据目录
        self.test_data_dir = project_root / "data" / "strategy_test"
        settings.data.raw_data_path = str(self.test_data_dir / "raw")
        settings.data.processed_data_path = str(self.test_data_dir / "processed")
        settings.data.database_path = str(self.test_data_dir / "test.duckdb")
        
        # 确保测试目录存在
        os.makedirs(settings.data.raw_data_path, exist_ok=True)
        os.makedirs(settings.data.processed_data_path, exist_ok=True)
        os.makedirs(os.path.dirname(settings.data.database_path), exist_ok=True)
    
    def test_get_stock_list(self):
        """测试获取股票列表"""
        try:
            stock_list = self.collector.get_stock_list()
            
            # 验证返回的是DataFrame
            self.assertIsNotNone(stock_list)
            self.assertTrue(len(stock_list) > 0)
            
            # 验证包含必要的列
            required_columns = ['code', 'name']
            for col in required_columns:
                self.assertIn(col, stock_list.columns)
            
            print(f"✓ 获取股票列表成功: {len(stock_list)} 只股票")
            
        except Exception as e:
            print(f"✗ 获取股票列表失败: {e}")
            # 网络问题不应该导致测试失败
            self.skipTest(f"网络连接问题: {e}")
    
    def test_get_stock_daily_data(self):
        """测试获取单只股票数据"""
        # 使用平安银行作为测试股票
        symbol = "000001"
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        
        try:
            data = self.collector.get_stock_daily_data(symbol, start_date, end_date)
            
            if data.empty:
                print(f"⚠ 股票 {symbol} 无数据，可能是网络问题或股票代码错误")
                return
            
            # 验证数据结构
            required_columns = ['symbol', 'date', 'open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                self.assertIn(col, data.columns, f"缺少列: {col}")
            
            # 验证数据类型
            self.assertTrue(data['date'].dtype.name.startswith('datetime'))
            self.assertTrue(data['open'].dtype.name in ['float64', 'int64'])
            
            # 验证价格逻辑
            self.assertTrue((data['high'] >= data['low']).all(), "最高价应该大于等于最低价")
            self.assertTrue((data['high'] >= data['open']).all(), "最高价应该大于等于开盘价")
            self.assertTrue((data['high'] >= data['close']).all(), "最高价应该大于等于收盘价")
            self.assertTrue((data['low'] <= data['open']).all(), "最低价应该小于等于开盘价")
            self.assertTrue((data['low'] <= data['close']).all(), "最低价应该小于等于收盘价")
            
            print(f"✓ 获取股票 {symbol} 数据成功: {len(data)} 条记录")
            print(f"  日期范围: {data['date'].min()} 到 {data['date'].max()}")
            
        except Exception as e:
            print(f"✗ 获取股票 {symbol} 数据失败: {e}")
            self.skipTest(f"网络连接问题: {e}")
    
    def test_data_validation(self):
        """测试数据验证功能"""
        from app.strategy.data.validator import DataValidator
        import pandas as pd
        
        validator = DataValidator()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'symbol': ['000001'] * 5,
            'date': pd.date_range('2024-01-01', periods=5),
            'open': [10.0, 10.5, 11.0, 10.8, 11.2],
            'high': [10.5, 11.0, 11.5, 11.2, 11.8],
            'low': [9.8, 10.2, 10.5, 10.5, 10.9],
            'close': [10.2, 10.8, 11.2, 10.9, 11.5],
            'volume': [1000000, 1200000, 800000, 1500000, 900000]
        })
        
        # 测试正常数据
        self.assertTrue(validator.validate_daily_data(test_data), "正常数据应该通过验证")
        
        # 测试异常数据 - 最高价小于最低价
        bad_data = test_data.copy()
        bad_data.loc[0, 'high'] = 9.0  # 最高价小于最低价
        self.assertFalse(validator.validate_daily_data(bad_data), "异常数据应该验证失败")
        
        print("✓ 数据验证功能正常")
    
    def test_data_transformation(self):
        """测试数据转换功能"""
        from app.strategy.data.transformer import DataTransformer
        import pandas as pd
        
        transformer = DataTransformer()
        
        # 创建模拟akshare格式数据
        akshare_data = pd.DataFrame({
            '日期': ['2024-01-01', '2024-01-02', '2024-01-03'],
            '开盘': [10.0, 10.5, 11.0],
            '收盘': [10.2, 10.8, 11.2],
            '最高': [10.5, 11.0, 11.5],
            '最低': [9.8, 10.2, 10.5],
            '成交量': [1000000, 1200000, 800000],
            '成交额': [10200000, 12960000, 8960000]
        })
        
        # 转换数据
        standardized_data = transformer.standardize_daily_data(akshare_data, '000001')
        
        # 验证转换结果
        expected_columns = ['symbol', 'date', 'open', 'high', 'low', 'close', 'volume', 'amount']
        for col in expected_columns:
            self.assertIn(col, standardized_data.columns, f"转换后应该包含列: {col}")
        
        # 验证股票代码
        self.assertEqual(standardized_data['symbol'].iloc[0], '000001')
        
        # 验证日期转换
        self.assertTrue(standardized_data['date'].dtype.name.startswith('datetime'))
        
        print("✓ 数据转换功能正常")
    
    def test_data_storage(self):
        """测试数据存储功能"""
        import pandas as pd
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'symbol': ['TEST001'] * 3,
            'date': pd.date_range('2024-01-01', periods=3),
            'open': [10.0, 10.5, 11.0],
            'high': [10.5, 11.0, 11.5],
            'low': [9.8, 10.2, 10.5],
            'close': [10.2, 10.8, 11.2],
            'volume': [1000000, 1200000, 800000]
        })
        
        # 测试按月保存
        self.collector.save_daily_data_by_month('TEST001', test_data)
        
        # 验证文件是否创建
        expected_file = Path(settings.data.raw_data_path) / "2024" / "01" / "TEST001.parquet"
        self.assertTrue(expected_file.exists(), f"应该创建文件: {expected_file}")
        
        # 验证文件内容
        saved_data = pd.read_parquet(expected_file)
        self.assertEqual(len(saved_data), 3, "保存的数据条数应该正确")
        
        print("✓ 数据存储功能正常")
    
    def test_data_loading(self):
        """测试数据加载功能"""
        # 先保存一些测试数据
        self.test_data_storage()
        
        # 测试加载数据
        loaded_data = self.manager.load_stock_data('TEST001', '2024-01-01', '2024-01-03')
        
        self.assertFalse(loaded_data.empty, "应该能加载到数据")
        self.assertEqual(len(loaded_data), 3, "加载的数据条数应该正确")
        
        print("✓ 数据加载功能正常")
    
    def tearDown(self):
        """测试后清理"""
        # 清理测试数据
        import shutil
        if self.test_data_dir.exists():
            shutil.rmtree(self.test_data_dir)

def run_tests():
    """运行所有测试"""
    print("=" * 60)
    print("量化分析系统 - 数据采集模块测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestDataCollector)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✓ 所有测试通过！")
    else:
        print(f"✗ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
    print("=" * 60)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    run_tests()
