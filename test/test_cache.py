#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试股票数据缓存功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


from app.utils.stock_api import StockAPI
import json

def test_cache_functionality():
    """测试缓存功能"""
    print("=== 股票数据缓存功能测试 ===\n")
    
    # 1. 获取缓存状态
    print("1. 初始缓存状态:")
    status = StockAPI.get_cache_status()
    print(json.dumps(status, ensure_ascii=False, indent=2))
    print()
    
    # 2. 获取股票数据（会触发缓存）
    print("2. 获取股票数据:")
    stock_info = StockAPI.get_stock_info('000001')
    if stock_info:
        print(f"股票信息: {stock_info.code} - {stock_info.name} - 价格: {stock_info.price}")
    else:
        print("获取股票信息失败")
    print()
    
    # 3. 再次获取缓存状态
    print("3. 获取数据后的缓存状态:")
    status = StockAPI.get_cache_status()
    print(json.dumps(status, ensure_ascii=False, indent=2))
    print()
    
    # 4. 测试市场状态判断
    print("4. 市场状态:")
    is_open = StockAPI._is_market_open()
    print(f"当前市场状态: {'开盘' if is_open else '休市'}")
    print()
    
    # 5. 测试缓存清理
    print("5. 清理缓存:")
    StockAPI.clear_cache()
    print()
    
    # 6. 清理后的缓存状态
    print("6. 清理后的缓存状态:")
    status = StockAPI.get_cache_status()
    print(json.dumps(status, ensure_ascii=False, indent=2))
    print()

if __name__ == '__main__':
    test_cache_functionality() 