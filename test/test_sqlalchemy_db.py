#!/usr/bin/env python3
"""
测试新的SQLAlchemy数据库系统
"""
import sys
import os
from pathlib import Path
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

from app.utils.db_manager_new import get_db_manager
from app.database.services.database_service import DatabaseService


def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    try:
        db_manager = get_db_manager()
        db_manager.connect()
        print("✓ 数据库连接成功")
        return True
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False


def test_favorite_stocks():
    """测试关注股票功能"""
    print("\n=== 测试关注股票功能 ===")
    try:
        db_manager = get_db_manager()
        
        # 添加关注股票
        success = db_manager.add_favorite_stock("000001", "平安银行", "测试股票1")
        print(f"添加关注股票: {'✓' if success else '✗'}")
        
        success = db_manager.add_favorite_stock("000002", "万科A", "测试股票2")
        print(f"添加关注股票: {'✓' if success else '✗'}")
        
        # 检查是否已关注
        is_favorited = db_manager.is_stock_favorited("000001")
        print(f"检查股票关注状态: {'✓' if is_favorited else '✗'}")
        
        # 获取关注股票列表
        stocks = db_manager.get_favorite_stocks()
        print(f"获取关注股票列表: ✓ (共{len(stocks)}个)")
        
        # 取消关注
        success = db_manager.remove_favorite_stock("000002")
        print(f"取消关注股票: {'✓' if success else '✗'}")
        
        return True
    except Exception as e:
        print(f"✗ 关注股票功能测试失败: {e}")
        return False


def test_favorite_reports():
    """测试收藏研报功能"""
    print("\n=== 测试收藏研报功能 ===")
    try:
        db_manager = get_db_manager()
        
        # 添加收藏研报
        success = db_manager.add_favorite_report("report_001", "测试研报1", "测试备注1")
        print(f"添加收藏研报: {'✓' if success else '✗'}")
        
        success = db_manager.add_favorite_report("report_002", "测试研报2", "测试备注2")
        print(f"添加收藏研报: {'✓' if success else '✗'}")
        
        # 检查是否已收藏
        is_favorited = db_manager.is_report_favorited("report_001")
        print(f"检查研报收藏状态: {'✓' if is_favorited else '✗'}")
        
        # 获取收藏研报列表
        reports = db_manager.get_favorite_reports()
        print(f"获取收藏研报列表: ✓ (共{len(reports)}个)")
        
        # 取消收藏
        success = db_manager.remove_favorite_report("report_002")
        print(f"取消收藏研报: {'✓' if success else '✗'}")
        
        return True
    except Exception as e:
        print(f"✗ 收藏研报功能测试失败: {e}")
        return False


def test_reports():
    """测试研报功能"""
    print("\n=== 测试研报功能 ===")
    try:
        db_manager = get_db_manager()
        
        # 创建测试研报数据
        report_data = {
            'id': str(uuid.uuid4()),
            'title': '测试研报标题',
            'content': '这是一个测试研报的内容',
            'label': '测试标签',
            'create_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'code_list': ['000001', '000002'],
            'source': 'test'
        }
        
        # 插入研报
        success = db_manager.insert_report(report_data)
        print(f"插入研报: {'✓' if success else '✗'}")
        
        if success:
            # 获取研报详情
            report = db_manager.get_report_by_id(report_data['id'])
            print(f"获取研报详情: {'✓' if report else '✗'}")
            
            # 更新研报评分
            success = db_manager.update_report_rating(report_data['id'], 8)
            print(f"更新研报评分: {'✓' if success else '✗'}")
            
            # 获取研报列表
            reports = db_manager.get_reports(page=1, page_size=10)
            print(f"获取研报列表: ✓ (共{len(reports)}个)")
            
            # 搜索研报
            search_results = db_manager.search_reports("测试")
            print(f"搜索研报: ✓ (找到{len(search_results)}个)")
            
            # 获取统计信息
            stats = db_manager.get_report_statistics()
            print(f"获取统计信息: ✓ (总数: {stats.get('total_count', 0)})")
        
        return True
    except Exception as e:
        print(f"✗ 研报功能测试失败: {e}")
        return False


def test_database_service():
    """测试数据库服务层"""
    print("\n=== 测试数据库服务层 ===")
    try:
        # 直接使用服务层
        success = DatabaseService.add_favorite_stock("service_test", "服务层测试", "通过服务层添加")
        print(f"服务层添加关注股票: {'✓' if success else '✗'}")
        
        stocks = DatabaseService.get_favorite_stocks()
        print(f"服务层获取关注股票: ✓ (共{len(stocks)}个)")
        
        return True
    except Exception as e:
        print(f"✗ 数据库服务层测试失败: {e}")
        return False


def test_performance():
    """测试性能"""
    print("\n=== 测试性能 ===")
    try:
        import time
        
        db_manager = get_db_manager()
        
        # 批量插入测试
        start_time = time.time()
        
        for i in range(10):
            report_data = {
                'id': f'perf_test_{i}',
                'title': f'性能测试研报 {i}',
                'content': f'这是第{i}个性能测试研报',
                'label': 'performance_test',
                'create_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'code_list': [f'00000{i % 3}'],
                'source': 'performance_test'
            }
            db_manager.insert_report(report_data)
        
        end_time = time.time()
        print(f"批量插入10个研报耗时: {end_time - start_time:.2f}秒")
        
        # 查询性能测试
        start_time = time.time()
        reports = db_manager.get_reports(page=1, page_size=100)
        end_time = time.time()
        print(f"查询100个研报耗时: {end_time - start_time:.2f}秒")
        
        return True
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        return False


def cleanup_test_data():
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    try:
        db_manager = get_db_manager()
        
        # 清理测试股票
        test_codes = ["000001", "000002", "service_test"]
        for code in test_codes:
            db_manager.remove_favorite_stock(code)
        
        # 清理测试研报收藏
        test_reports = ["report_001", "report_002"]
        for report_id in test_reports:
            db_manager.remove_favorite_report(report_id)
        
        print("✓ 测试数据清理完成")
        return True
    except Exception as e:
        print(f"✗ 清理测试数据失败: {e}")
        return False


def main():
    """主测试函数"""
    print("SQLAlchemy数据库系统测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("数据库连接", test_database_connection()))
    test_results.append(("关注股票功能", test_favorite_stocks()))
    test_results.append(("收藏研报功能", test_favorite_reports()))
    test_results.append(("研报功能", test_reports()))
    test_results.append(("数据库服务层", test_database_service()))
    test_results.append(("性能测试", test_performance()))
    
    # 清理测试数据
    cleanup_test_data()
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！新的SQLAlchemy数据库系统工作正常。")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
