#!/usr/bin/env python3
"""
测试研报删除功能的脚本
用于验证删除功能的改造是否正常工作
"""

import requests
import json
import sys
from typing import Dict, Any

# 配置
BASE_URL = "http://localhost:8000"  # 根据实际情况修改
TEST_REPORT_ID = "test_report_001"  # 需要替换为实际的研报ID

def test_delete_report(report_id: str) -> Dict[str, Any]:
    """测试删除研报功能"""
    url = f"{BASE_URL}/reports/manage/api/{report_id}"
    
    # 测试快速删除（无删除原因）
    payload = {"reason": ""}
    
    try:
        response = requests.delete(url, json=payload)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"status": "error", "message": str(e)}

def test_restore_report(report_id: str) -> Dict[str, Any]:
    """测试恢复研报功能"""
    url = f"{BASE_URL}/reports/manage/api/{report_id}/restore"
    
    try:
        response = requests.post(url)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"status": "error", "message": str(e)}

def test_get_report_status(report_id: str) -> Dict[str, Any]:
    """测试获取研报状态"""
    url = f"{BASE_URL}/reports/manage/api/{report_id}/status"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"status": "error", "message": str(e)}

def main():
    """主测试函数"""
    if len(sys.argv) > 1:
        report_id = sys.argv[1]
    else:
        report_id = TEST_REPORT_ID
        print(f"使用默认测试研报ID: {report_id}")
        print("可以通过命令行参数指定其他研报ID")
    
    print("=" * 50)
    print("研报删除功能测试")
    print("=" * 50)
    
    # 1. 获取初始状态
    print("\n1. 获取研报初始状态...")
    status = test_get_report_status(report_id)
    print(f"状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
    
    if status.get("status") != "success":
        print("❌ 无法获取研报状态，请检查研报ID是否正确")
        return
    
    initial_deleted = status.get("data", {}).get("is_deleted", False)
    print(f"初始删除状态: {initial_deleted}")
    
    # 2. 测试删除功能
    print("\n2. 测试快速删除功能...")
    delete_result = test_delete_report(report_id)
    print(f"删除结果: {json.dumps(delete_result, indent=2, ensure_ascii=False)}")
    
    if delete_result.get("status") == "success":
        print("✅ 快速删除成功")
    else:
        print("❌ 快速删除失败")
        return
    
    # 3. 验证删除状态
    print("\n3. 验证删除后状态...")
    status_after_delete = test_get_report_status(report_id)
    print(f"删除后状态: {json.dumps(status_after_delete, indent=2, ensure_ascii=False)}")
    
    if status_after_delete.get("data", {}).get("is_deleted"):
        print("✅ 研报已正确标记为删除")
    else:
        print("❌ 研报删除状态不正确")
    
    # 4. 测试恢复功能
    print("\n4. 测试恢复功能...")
    restore_result = test_restore_report(report_id)
    print(f"恢复结果: {json.dumps(restore_result, indent=2, ensure_ascii=False)}")
    
    if restore_result.get("status") == "success":
        print("✅ 恢复成功")
    else:
        print("❌ 恢复失败")
        return
    
    # 5. 验证恢复状态
    print("\n5. 验证恢复后状态...")
    status_after_restore = test_get_report_status(report_id)
    print(f"恢复后状态: {json.dumps(status_after_restore, indent=2, ensure_ascii=False)}")
    
    if not status_after_restore.get("data", {}).get("is_deleted"):
        print("✅ 研报已正确恢复")
    else:
        print("❌ 研报恢复状态不正确")
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
