import os
import sys

def create_directory_structure():
    """创建项目所需的目录结构"""
    directories = [
        "app/static/css",
        "app/static/js",
        "app/templates",
        "app/routers",
        "app/utils",
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"创建目录: {directory}")
    
    # 创建空的__init__.py文件
    init_files = [
        "app/__init__.py",
        "app/routers/__init__.py",
        "app/utils/__init__.py",
    ]
    
    for init_file in init_files:
        with open(init_file, 'w') as f:
            pass
        print(f"创建文件: {init_file}")
    
    print("目录结构创建完成！")

if __name__ == "__main__":
    create_directory_structure() 