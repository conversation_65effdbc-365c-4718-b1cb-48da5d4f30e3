# 研报展示平台

这是一个基于FastAPI和Bootstrap开发的研报展示平台，用于展示和查询股票研报数据。

## 功能特点

- 研报内容展示：清晰展示研报标题、内容、发布时间等信息
- 按日期筛选：支持按日期筛选研报
- 按标签筛选：支持按研报标签筛选
- 股票代码搜索：支持按股票代码搜索相关研报
- 股票实时行情：集成akshare获取股票实时行情
- 东方财富链接：提供东方财富股票页面链接

## 技术栈

- 后端：FastAPI
- 前端：Bootstrap 5 + 原生JavaScript
- 模板引擎：Jinja2
- 数据获取：akshare

## 项目结构

```
.
├── app                     # 应用主目录
│   ├── main.py            # 主应用入口
│   ├── models.py          # 数据模型
│   ├── config.py          # 配置文件
│   ├── routers            # 路由目录
│   │   ├── reports.py     # 研报相关路由
│   │   └── stocks.py      # 股票相关路由
│   ├── static             # 静态资源
│   │   ├── css            # CSS样式
│   │   └── js             # JavaScript脚本
│   ├── templates          # HTML模板
│   └── utils              # 工具函数
│       ├── data_loader.py # 数据加载器
│       └── stock_api.py   # 股票API
├── dify                   # 数据处理脚本目录
├── .env                   # 环境变量配置文件
├── requirements.txt       # 项目依赖
├── run.py                 # 启动脚本
└── README.md              # 项目说明
```

## 安装与运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制`config.env.example`为`.env`，并根据需要修改配置：

```bash
cp config.env.example .env
```

主要配置项：
- `API_HOST` 和 `API_PORT`：API服务的主机和端口
- `REPORT_DATA_DIR`：研报数据存储目录，默认为`/Users/<USER>/PycharmProjects/own/data/kpl_report`

### 3. 运行应用

```bash
python run.py
```

应用将在 http://localhost:8000 启动。

## API接口

### 研报相关

- `GET /reports` - 获取研报列表页面
- `GET /reports/{report_id}` - 获取研报详情页面
- `GET /reports/api/list` - 获取研报列表数据API
- `GET /reports/api/{report_id}` - 获取研报详情数据API
- `GET /reports/api/filters` - 获取筛选条件API

### 股票相关

- `GET /stocks/{code}` - 获取股票详情页面
- `GET /stocks/api/{code}` - 获取股票信息API
- `GET /stocks/api/{code}/reports` - 获取股票相关研报API

## 数据格式

研报数据格式示例：

```json
{
  "Id": 214084,
  "Title": "研报标题",
  "Files": [],
  "CreateTime": "2025-07-08 09:09:22",
  "Label": "事件点评",
  "Conts": "<p>研报内容...</p>",
  "art_url": "https://example.com/report",
  "code_list": ["000001", "600000"]
}
```

## 扩展开发

如需扩展功能，可以：

1. 在 `app/routers` 目录下添加新的路由
2. 在 `app/templates` 目录下添加新的页面模板
3. 在 `app/utils` 目录下添加新的工具函数

## 注意事项

- 请确保配置的 `REPORT_DATA_DIR` 目录下有正确格式的研报数据
- 股票实时行情依赖于akshare库，请确保网络连接正常 