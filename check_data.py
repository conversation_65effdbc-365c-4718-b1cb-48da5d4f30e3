import pandas as pd
import os
import glob

print("=== 检查数据结构 ===")

# 查看原始数据样例
raw_file = 'data/strategy/raw/2025/01/20250102.parquet'
print(f'检查文件: {raw_file}')
print(f'文件存在: {os.path.exists(raw_file)}')

if os.path.exists(raw_file):
    try:
        df = pd.read_parquet(raw_file)
        print('\n原始数据结构:')
        print(df.head())
        print(f'\n数据形状: {df.shape}')
        print(f'列名: {list(df.columns)}')
        print(f'\n数据类型:')
        print(df.dtypes)

        # 检查股票数量
        if 'symbol' in df.columns:
            print(f'\n股票数量: {df["symbol"].nunique()}')
            print(f'股票代码示例: {df["symbol"].unique()[:5]}')
    except Exception as e:
        print(f'读取文件失败: {e}')
else:
    print('原始数据文件不存在')
    # 查看目录内容
    pattern = 'data/strategy/raw/2024/07/*.parquet'
    files = glob.glob(pattern)
    print(f'找到的文件: {files}')

    # 查看更多目录
    pattern2 = 'data/strategy/raw/2025/01/*.parquet'
    files2 = glob.glob(pattern2)
    print(f'2025年1月文件: {files2[:3]}')

# 检查现有指标数据
print("\n=== 检查现有指标数据 ===")
indicator_db = 'data/strategy/processed/indicators.duckdb'
print(f'指标数据库存在: {os.path.exists(indicator_db)}')

if os.path.exists(indicator_db):
    try:
        import duckdb
        conn = duckdb.connect(indicator_db)
        tables = conn.execute("SHOW TABLES").fetchall()
        print(f'数据库表: {tables}')

        if tables:
            # 查看第一个表的结构
            table_name = tables[0][0]
            schema = conn.execute(f"DESCRIBE {table_name}").fetchall()
            print(f'\n表 {table_name} 结构:')
            for col in schema[:10]:  # 只显示前10列
                print(f'  {col[0]}: {col[1]}')
        conn.close()
    except Exception as e:
        print(f'读取数据库失败: {e}')
