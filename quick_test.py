#!/usr/bin/env python3
"""
快速验证脚本
"""
import os
import sys
from pathlib import Path

print("🧪 快速验证开始")

# 检查Python版本
print(f"Python版本: {sys.version}")

# 检查当前目录
print(f"当前目录: {os.getcwd()}")

# 检查项目结构
project_root = Path(".")
print(f"项目根目录: {project_root.absolute()}")

# 检查关键目录
key_dirs = [
    "app/strategy",
    "app/strategy/indicators", 
    "app/strategy/services",
    "data/strategy/raw",
    "data/strategy/processed"
]

for dir_path in key_dirs:
    path = Path(dir_path)
    exists = path.exists()
    print(f"目录 {dir_path}: {'✅' if exists else '❌'}")

# 检查数据文件
raw_data_path = Path("data/strategy/raw")
if raw_data_path.exists():
    # 查找最新的数据文件
    parquet_files = list(raw_data_path.rglob("*.parquet"))
    print(f"原始数据文件数量: {len(parquet_files)}")
    if parquet_files:
        latest_file = max(parquet_files, key=lambda x: x.stat().st_mtime)
        print(f"最新数据文件: {latest_file}")

# 检查已有的指标数据
processed_path = Path("data/strategy/processed")
if processed_path.exists():
    indicator_files = list(processed_path.rglob("*.parquet"))
    duckdb_files = list(processed_path.rglob("*.duckdb"))
    print(f"已有指标Parquet文件: {len(indicator_files)}")
    print(f"已有DuckDB文件: {len(duckdb_files)}")

print("\n🎯 建议的下一步操作:")
print("1. 运行简单测试: D:\\miniconda3\\envs\\py311\\python.exe simple_test.py")
print("2. 查看指标状态: D:\\miniconda3\\envs\\py311\\python.exe scripts/calculate_indicators_batch.py --mode status")
print("3. 运行增量计算: D:\\miniconda3\\envs\\py311\\python.exe scripts/update_indicators_incremental.py --status")

print("\n✅ 快速验证完成")
