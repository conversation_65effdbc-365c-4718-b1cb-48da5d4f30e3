#!/usr/bin/env python3
"""
指标计算测试脚本

用于快速测试和验证指标计算功能
"""
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import pandas as pd
from app.strategy.indicators.batch_calculator import BatchIndicatorCalculator
from app.strategy.indicators.parquet_storage import ParquetIndicatorStorage
from app.strategy.services.data_service import DataService
from app.strategy.indicators.engine import IndicatorEngine


def test_data_loading():
    """测试数据加载"""
    print("=== 测试数据加载 ===")
    
    data_service = DataService()
    
    # 测试加载最近的数据
    recent_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    print(f"测试加载日期: {recent_date}")
    
    daily_data = data_service.load_daily_data(recent_date)
    if not daily_data.empty:
        print(f"✅ 成功加载 {len(daily_data)} 条记录")
        print(f"股票数量: {daily_data['symbol'].nunique()}")
        print(f"列名: {list(daily_data.columns)}")
        print("\n前5条记录:")
        print(daily_data.head())
    else:
        print(f"❌ 没有找到日期 {recent_date} 的数据")
        
        # 尝试其他日期
        for i in range(2, 8):
            test_date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
            test_data = data_service.load_daily_data(test_date)
            if not test_data.empty:
                print(f"✅ 找到日期 {test_date} 的数据: {len(test_data)} 条记录")
                break
        else:
            print("❌ 最近7天都没有数据")


def test_indicator_calculation():
    """测试指标计算"""
    print("\n=== 测试指标计算 ===")
    
    data_service = DataService()
    engine = IndicatorEngine()
    
    # 获取一只股票的数据进行测试
    recent_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    daily_data = data_service.load_daily_data(recent_date)
    
    if daily_data.empty:
        print("❌ 没有测试数据")
        return
    
    # 选择第一只股票
    test_symbol = daily_data['symbol'].iloc[0]
    print(f"测试股票: {test_symbol}")
    
    # 加载该股票的历史数据
    stock_data = data_service.load_stock_history(test_symbol, limit=100)
    if stock_data.empty:
        print(f"❌ 股票 {test_symbol} 没有历史数据")
        return
    
    print(f"历史数据: {len(stock_data)} 条记录")
    print(f"日期范围: {stock_data['date'].min()} 到 {stock_data['date'].max()}")
    
    # 计算指标
    print("计算指标中...")
    try:
        indicators = engine.calculate_all_indicators(stock_data)
        if not indicators.empty:
            print(f"✅ 成功计算指标: {len(indicators)} 条记录")
            print(f"指标列数: {len(indicators.columns)}")
            
            # 显示最新一条记录的部分指标
            latest = indicators.iloc[-1]
            print("\n最新指标值:")
            indicator_cols = [col for col in indicators.columns if col not in ['symbol', 'date', 'open', 'high', 'low', 'close', 'volume']]
            for col in indicator_cols[:10]:  # 只显示前10个指标
                if pd.notna(latest[col]):
                    print(f"  {col}: {latest[col]:.4f}")
        else:
            print("❌ 指标计算结果为空")
    except Exception as e:
        print(f"❌ 指标计算失败: {e}")


def test_parquet_storage():
    """测试Parquet存储"""
    print("\n=== 测试Parquet存储 ===")
    
    storage = ParquetIndicatorStorage()
    
    # 测试状态查询
    print("查询存储状态...")
    earliest_date, latest_date = storage.get_date_range()
    symbols = storage.get_available_symbols()
    
    print(f"日期范围: {earliest_date} 到 {latest_date}")
    print(f"股票数量: {len(symbols)}")
    
    if symbols:
        print(f"股票示例: {symbols[:5]}")
    
    # 测试加载数据
    if latest_date:
        print(f"\n测试加载日期 {latest_date} 的数据...")
        date_data = storage.load_indicators_by_date(latest_date)
        if not date_data.empty:
            print(f"✅ 成功加载: {len(date_data)} 条记录")
        else:
            print("❌ 没有数据")
    
    if symbols:
        test_symbol = symbols[0]
        print(f"\n测试加载股票 {test_symbol} 的数据...")
        symbol_data = storage.load_indicators_by_symbol(test_symbol)
        if not symbol_data.empty:
            print(f"✅ 成功加载: {len(symbol_data)} 条记录")
            print(f"日期范围: {symbol_data['date'].min()} 到 {symbol_data['date'].max()}")
        else:
            print("❌ 没有数据")


def test_batch_calculation():
    """测试批量计算"""
    print("\n=== 测试批量计算 ===")
    
    calculator = BatchIndicatorCalculator(max_workers=2)
    data_service = DataService()
    
    # 找一个有数据的日期
    test_date = None
    for i in range(1, 8):
        check_date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
        daily_data = data_service.load_daily_data(check_date)
        if not daily_data.empty:
            test_date = check_date
            break
    
    if not test_date:
        print("❌ 没有找到测试数据")
        return
    
    print(f"测试日期: {test_date}")
    
    # 获取少量股票进行测试
    daily_data = data_service.load_daily_data(test_date)
    test_symbols = daily_data['symbol'].unique()[:3].tolist()  # 只测试3只股票
    
    print(f"测试股票: {test_symbols}")
    print("开始批量计算...")
    
    try:
        result = calculator.calculate_indicators_for_date(
            date=test_date,
            symbols=test_symbols,
            force_recalculate=True
        )
        
        if result['success']:
            print(f"✅ 批量计算成功:")
            print(f"  总股票: {result['total_symbols']}")
            print(f"  计算成功: {result['calculated']}")
            print(f"  计算失败: {result['failed']}")
            print(f"  耗时: {result['elapsed_time']:.2f}s")
        else:
            print(f"❌ 批量计算失败: {result.get('error', '未知错误')}")
    except Exception as e:
        print(f"❌ 批量计算异常: {e}")


def test_incremental_update():
    """测试增量更新"""
    print("\n=== 测试增量更新 ===")
    
    calculator = BatchIndicatorCalculator(max_workers=2)
    
    # 测试最近3天的增量更新
    end_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
    
    print(f"测试增量更新: {start_date} 到 {end_date}")
    
    try:
        result = calculator.calculate_indicators_incremental(
            start_date=start_date,
            end_date=end_date
        )
        
        if result['success']:
            print(f"✅ 增量更新成功:")
            print(f"  总日期: {result['total_dates']}")
            print(f"  成功日期: {result['calculated_dates']}")
            print(f"  失败日期: {result['failed_dates']}")
            print(f"  耗时: {result['elapsed_time']:.2f}s")
        else:
            print(f"❌ 增量更新失败: {result.get('error', '未知错误')}")
    except Exception as e:
        print(f"❌ 增量更新异常: {e}")


def main():
    """主函数"""
    print("🧪 开始指标计算测试\n")
    
    try:
        # 依次执行各项测试
        test_data_loading()
        test_indicator_calculation()
        test_parquet_storage()
        test_batch_calculation()
        test_incremental_update()
        
        print("\n🎉 所有测试完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
