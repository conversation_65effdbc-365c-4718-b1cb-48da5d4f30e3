#!/usr/bin/env python3
"""
DuckDB指标数据迁移到Parquet格式脚本

将现有的DuckDB中的指标数据迁移到新的Parquet存储格式
"""
import sys
import os
import argparse
import logging
from pathlib import Path
from datetime import datetime
import pandas as pd
import duckdb

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.indicators.parquet_storage import ParquetIndicatorStorage
from app.strategy.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/migration.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class DuckDBToParquetMigrator:
    """DuckDB到Parquet迁移器"""
    
    def __init__(self):
        self.parquet_storage = ParquetIndicatorStorage()
        self.duckdb_path = Path(settings.data.processed_data_path) / "indicators.duckdb"
        
        if not self.duckdb_path.exists():
            raise FileNotFoundError(f"DuckDB文件不存在: {self.duckdb_path}")
    
    def analyze_duckdb_data(self) -> dict:
        """分析DuckDB中的数据"""
        try:
            with duckdb.connect(str(self.duckdb_path)) as conn:
                # 获取所有表
                tables = conn.execute("SHOW TABLES").fetchall()
                
                analysis = {
                    'tables': [],
                    'total_records': 0,
                    'date_range': None,
                    'symbols_count': 0
                }
                
                for table in tables:
                    table_name = table[0]
                    
                    # 获取表结构
                    schema = conn.execute(f"DESCRIBE {table_name}").fetchall()
                    
                    # 获取记录数
                    count = conn.execute(f"SELECT COUNT(*) FROM {table_name}").fetchone()[0]
                    
                    table_info = {
                        'name': table_name,
                        'columns': [col[0] for col in schema],
                        'record_count': count
                    }
                    
                    # 如果是主要的指标表，获取更多信息
                    if 'indicator' in table_name.lower() or table_name == 'stock_indicators':
                        try:
                            # 获取日期范围
                            date_range = conn.execute(f"""
                                SELECT MIN(date) as min_date, MAX(date) as max_date 
                                FROM {table_name} 
                                WHERE date IS NOT NULL
                            """).fetchone()
                            
                            if date_range and date_range[0]:
                                table_info['date_range'] = (str(date_range[0]), str(date_range[1]))
                                analysis['date_range'] = table_info['date_range']
                            
                            # 获取股票数量
                            if 'symbol' in table_info['columns']:
                                symbols_count = conn.execute(f"""
                                    SELECT COUNT(DISTINCT symbol) FROM {table_name}
                                """).fetchone()[0]
                                table_info['symbols_count'] = symbols_count
                                analysis['symbols_count'] = symbols_count
                                
                        except Exception as e:
                            logger.warning(f"获取表 {table_name} 详细信息失败: {e}")
                    
                    analysis['tables'].append(table_info)
                    analysis['total_records'] += count
                
                return analysis
                
        except Exception as e:
            logger.error(f"分析DuckDB数据失败: {e}")
            return {}
    
    def migrate_by_symbol(self, batch_size: int = 100) -> dict:
        """按股票迁移数据"""
        try:
            with duckdb.connect(str(self.duckdb_path)) as conn:
                # 获取所有股票代码
                symbols = conn.execute("""
                    SELECT DISTINCT symbol 
                    FROM stock_indicators 
                    WHERE symbol IS NOT NULL 
                    ORDER BY symbol
                """).fetchall()
                
                symbols = [s[0] for s in symbols]
                logger.info(f"需要迁移的股票数量: {len(symbols)}")
                
                migrated_count = 0
                failed_count = 0
                
                # 分批处理
                for i in range(0, len(symbols), batch_size):
                    batch_symbols = symbols[i:i + batch_size]
                    logger.info(f"处理批次 {i//batch_size + 1}: {len(batch_symbols)} 只股票")
                    
                    for symbol in batch_symbols:
                        try:
                            # 从DuckDB读取该股票的所有指标数据
                            data = conn.execute("""
                                SELECT * FROM stock_indicators 
                                WHERE symbol = ? 
                                ORDER BY date
                            """, [symbol]).df()
                            
                            if not data.empty:
                                # 保存到Parquet
                                success = self.parquet_storage.save_indicators_by_symbol(symbol, data)
                                if success:
                                    migrated_count += 1
                                    logger.debug(f"迁移股票 {symbol} 成功: {len(data)} 条记录")
                                else:
                                    failed_count += 1
                                    logger.error(f"迁移股票 {symbol} 失败")
                            else:
                                logger.warning(f"股票 {symbol} 没有数据")
                                
                        except Exception as e:
                            failed_count += 1
                            logger.error(f"迁移股票 {symbol} 异常: {e}")
                
                return {
                    'success': True,
                    'total_symbols': len(symbols),
                    'migrated': migrated_count,
                    'failed': failed_count
                }
                
        except Exception as e:
            logger.error(f"按股票迁移失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def migrate_by_date(self, batch_size: int = 10) -> dict:
        """按日期迁移数据"""
        try:
            with duckdb.connect(str(self.duckdb_path)) as conn:
                # 获取所有日期
                dates = conn.execute("""
                    SELECT DISTINCT date 
                    FROM stock_indicators 
                    WHERE date IS NOT NULL 
                    ORDER BY date
                """).fetchall()
                
                dates = [str(d[0]) for d in dates]
                logger.info(f"需要迁移的日期数量: {len(dates)}")
                
                migrated_count = 0
                failed_count = 0
                
                # 分批处理
                for i in range(0, len(dates), batch_size):
                    batch_dates = dates[i:i + batch_size]
                    logger.info(f"处理批次 {i//batch_size + 1}: {len(batch_dates)} 个日期")
                    
                    for date in batch_dates:
                        try:
                            # 从DuckDB读取该日期的所有指标数据
                            data = conn.execute("""
                                SELECT * FROM stock_indicators 
                                WHERE date = ? 
                                ORDER BY symbol
                            """, [date]).df()
                            
                            if not data.empty:
                                # 保存到Parquet
                                success = self.parquet_storage.save_indicators_by_date(date, data)
                                if success:
                                    migrated_count += 1
                                    logger.debug(f"迁移日期 {date} 成功: {len(data)} 条记录")
                                else:
                                    failed_count += 1
                                    logger.error(f"迁移日期 {date} 失败")
                            else:
                                logger.warning(f"日期 {date} 没有数据")
                                
                        except Exception as e:
                            failed_count += 1
                            logger.error(f"迁移日期 {date} 异常: {e}")
                
                return {
                    'success': True,
                    'total_dates': len(dates),
                    'migrated': migrated_count,
                    'failed': failed_count
                }
                
        except Exception as e:
            logger.error(f"按日期迁移失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def verify_migration(self) -> dict:
        """验证迁移结果"""
        try:
            # 检查Parquet数据
            parquet_symbols = self.parquet_storage.get_available_symbols()
            earliest_date, latest_date = self.parquet_storage.get_date_range()
            
            # 检查DuckDB数据
            with duckdb.connect(str(self.duckdb_path)) as conn:
                duckdb_symbols = conn.execute("""
                    SELECT COUNT(DISTINCT symbol) FROM stock_indicators
                """).fetchone()[0]
                
                duckdb_date_range = conn.execute("""
                    SELECT MIN(date), MAX(date) FROM stock_indicators
                """).fetchone()
            
            verification = {
                'parquet': {
                    'symbols_count': len(parquet_symbols),
                    'date_range': (earliest_date, latest_date)
                },
                'duckdb': {
                    'symbols_count': duckdb_symbols,
                    'date_range': (str(duckdb_date_range[0]), str(duckdb_date_range[1]))
                }
            }
            
            # 比较结果
            verification['symbols_match'] = len(parquet_symbols) == duckdb_symbols
            verification['date_range_match'] = (earliest_date == verification['duckdb']['date_range'][0] and 
                                              latest_date == verification['duckdb']['date_range'][1])
            
            return verification
            
        except Exception as e:
            logger.error(f"验证迁移结果失败: {e}")
            return {}


def main():
    parser = argparse.ArgumentParser(description='DuckDB指标数据迁移到Parquet')
    
    parser.add_argument('--mode', choices=['analyze', 'migrate-symbol', 'migrate-date', 'verify'], 
                       default='analyze', help='操作模式')
    parser.add_argument('--batch-size', type=int, default=100, help='批处理大小')
    parser.add_argument('--force', action='store_true', help='强制执行迁移')
    
    args = parser.parse_args()
    
    try:
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
        
        migrator = DuckDBToParquetMigrator()
        
        if args.mode == 'analyze':
            print("📊 分析DuckDB数据...")
            analysis = migrator.analyze_duckdb_data()
            
            if analysis:
                print(f"\n数据库分析结果:")
                print(f"  表数量: {len(analysis['tables'])}")
                print(f"  总记录数: {analysis['total_records']}")
                print(f"  股票数量: {analysis['symbols_count']}")
                print(f"  日期范围: {analysis['date_range']}")
                
                print(f"\n表详情:")
                for table in analysis['tables']:
                    print(f"  {table['name']}: {table['record_count']} 条记录")
                    if 'date_range' in table:
                        print(f"    日期范围: {table['date_range']}")
                    if 'symbols_count' in table:
                        print(f"    股票数量: {table['symbols_count']}")
            
        elif args.mode == 'migrate-symbol':
            if not args.force:
                confirm = input("确认按股票迁移数据？这将创建大量文件 (y/n): ").lower().strip()
                if confirm != 'y':
                    print("已取消")
                    return
            
            print("🚀 开始按股票迁移数据...")
            result = migrator.migrate_by_symbol(args.batch_size)
            
            if result['success']:
                print(f"✅ 迁移完成:")
                print(f"  总股票: {result['total_symbols']}")
                print(f"  成功: {result['migrated']}")
                print(f"  失败: {result['failed']}")
            else:
                print(f"❌ 迁移失败: {result.get('error', '未知错误')}")
        
        elif args.mode == 'migrate-date':
            if not args.force:
                confirm = input("确认按日期迁移数据？(y/n): ").lower().strip()
                if confirm != 'y':
                    print("已取消")
                    return
            
            print("🚀 开始按日期迁移数据...")
            result = migrator.migrate_by_date(args.batch_size)
            
            if result['success']:
                print(f"✅ 迁移完成:")
                print(f"  总日期: {result['total_dates']}")
                print(f"  成功: {result['migrated']}")
                print(f"  失败: {result['failed']}")
            else:
                print(f"❌ 迁移失败: {result.get('error', '未知错误')}")
        
        elif args.mode == 'verify':
            print("🔍 验证迁移结果...")
            verification = migrator.verify_migration()
            
            if verification:
                print(f"\n验证结果:")
                print(f"  Parquet股票数: {verification['parquet']['symbols_count']}")
                print(f"  DuckDB股票数: {verification['duckdb']['symbols_count']}")
                print(f"  股票数匹配: {'✅' if verification['symbols_match'] else '❌'}")
                
                print(f"  Parquet日期范围: {verification['parquet']['date_range']}")
                print(f"  DuckDB日期范围: {verification['duckdb']['date_range']}")
                print(f"  日期范围匹配: {'✅' if verification['date_range_match'] else '❌'}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"执行失败: {e}")
        print(f"❌ 执行失败: {e}")


if __name__ == '__main__':
    main()
